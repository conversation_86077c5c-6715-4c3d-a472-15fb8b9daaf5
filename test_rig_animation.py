#!/usr/bin/env python3
"""
Test the rig with various animations to ensure it's working properly
"""

from augment_blender_interface import AugmentBlenderInterface

def test_rig_animation():
    """Test the rig with sample animations"""
    
    animation_code = '''
import bpy
import mathutils
from mathutils import Vector, Euler
import math

print("🎬 Testing rig with animations...")

# Find the rig
human_rig = None
for obj in bpy.data.objects:
    if obj.type == 'ARMATURE':
        human_rig = obj
        break

if not human_rig:
    print("❌ No armature found!")
else:
    print(f"✅ Found rig: {human_rig.name}")
    
    # Set up animation
    bpy.context.view_layer.objects.active = human_rig
    bpy.ops.object.mode_set(mode='POSE')
    
    # Clear existing animation
    if human_rig.animation_data:
        human_rig.animation_data_clear()
    
    # Create new action
    action = bpy.data.actions.new(name="Test_Animation")
    human_rig.animation_data_create()
    human_rig.animation_data.action = action
    
    # Set frame range
    bpy.context.scene.frame_start = 1
    bpy.context.scene.frame_end = 120
    bpy.context.scene.frame_set(1)
    
    print("🎯 Creating test animations...")
    
    # Test 1: Wave animation
    def create_wave_animation():
        """Create a simple wave animation"""
        print("  📋 Creating wave animation...")
        
        # Find arm bones
        arm_bones = []
        for bone in human_rig.pose.bones:
            if any(keyword in bone.name.lower() for keyword in ['arm', 'shoulder', 'hand']):
                arm_bones.append(bone)
        
        if arm_bones:
            # Animate the first arm bone found
            arm_bone = arm_bones[0]
            print(f"    Animating bone: {arm_bone.name}")
            
            # Keyframe 1: Start position
            bpy.context.scene.frame_set(1)
            arm_bone.rotation_euler = (0, 0, 0)
            arm_bone.keyframe_insert(data_path="rotation_euler")
            
            # Keyframe 2: Raised arm
            bpy.context.scene.frame_set(30)
            arm_bone.rotation_euler = (0, 0, math.radians(90))
            arm_bone.keyframe_insert(data_path="rotation_euler")
            
            # Keyframe 3: Wave motion
            bpy.context.scene.frame_set(60)
            arm_bone.rotation_euler = (0, 0, math.radians(70))
            arm_bone.keyframe_insert(data_path="rotation_euler")
            
            # Keyframe 4: Back to raised
            bpy.context.scene.frame_set(90)
            arm_bone.rotation_euler = (0, 0, math.radians(90))
            arm_bone.keyframe_insert(data_path="rotation_euler")
            
            # Keyframe 5: Back to start
            bpy.context.scene.frame_set(120)
            arm_bone.rotation_euler = (0, 0, 0)
            arm_bone.keyframe_insert(data_path="rotation_euler")
            
            print("    ✅ Wave animation created")
        else:
            print("    ❌ No arm bones found")
    
    # Test 2: Simple walk cycle (leg movement)
    def create_walk_test():
        """Create a simple leg movement test"""
        print("  🚶 Creating walk test...")
        
        # Find leg bones
        leg_bones = []
        for bone in human_rig.pose.bones:
            if any(keyword in bone.name.lower() for keyword in ['leg', 'thigh', 'foot']):
                leg_bones.append(bone)
        
        if leg_bones:
            leg_bone = leg_bones[0]
            print(f"    Animating bone: {leg_bone.name}")
            
            # Simple leg swing
            bpy.context.scene.frame_set(1)
            leg_bone.rotation_euler = (0, 0, 0)
            leg_bone.keyframe_insert(data_path="rotation_euler")
            
            bpy.context.scene.frame_set(60)
            leg_bone.rotation_euler = (math.radians(20), 0, 0)
            leg_bone.keyframe_insert(data_path="rotation_euler")
            
            bpy.context.scene.frame_set(120)
            leg_bone.rotation_euler = (0, 0, 0)
            leg_bone.keyframe_insert(data_path="rotation_euler")
            
            print("    ✅ Walk test created")
        else:
            print("    ❌ No leg bones found")
    
    # Test 3: Head turn
    def create_head_turn():
        """Create a head turning animation"""
        print("  🗣️ Creating head turn...")
        
        # Find head/neck bones
        head_bones = []
        for bone in human_rig.pose.bones:
            if any(keyword in bone.name.lower() for keyword in ['head', 'neck']):
                head_bones.append(bone)
        
        if head_bones:
            head_bone = head_bones[0]
            print(f"    Animating bone: {head_bone.name}")
            
            # Head turn animation
            bpy.context.scene.frame_set(1)
            head_bone.rotation_euler = (0, 0, 0)
            head_bone.keyframe_insert(data_path="rotation_euler")
            
            bpy.context.scene.frame_set(40)
            head_bone.rotation_euler = (0, 0, math.radians(45))
            head_bone.keyframe_insert(data_path="rotation_euler")
            
            bpy.context.scene.frame_set(80)
            head_bone.rotation_euler = (0, 0, math.radians(-45))
            head_bone.keyframe_insert(data_path="rotation_euler")
            
            bpy.context.scene.frame_set(120)
            head_bone.rotation_euler = (0, 0, 0)
            head_bone.keyframe_insert(data_path="rotation_euler")
            
            print("    ✅ Head turn created")
        else:
            print("    ❌ No head bones found")
    
    # Create all test animations
    create_wave_animation()
    create_walk_test()
    create_head_turn()
    
    # Set interpolation to smooth
    for fcurve in action.fcurves:
        for keyframe in fcurve.keyframe_points:
            keyframe.interpolation = 'BEZIER'
            keyframe.handle_left_type = 'AUTO'
            keyframe.handle_right_type = 'AUTO'
    
    # Go back to frame 1
    bpy.context.scene.frame_set(1)
    
    print("✅ Animation tests complete!")
    print("🎬 Press SPACE to play the animation")
    print("📋 Animation includes:")
    print("  - Wave motion (arm)")
    print("  - Walk test (leg)")
    print("  - Head turn")
    
    # Exit pose mode
    bpy.ops.object.mode_set(mode='OBJECT')

print("🎯 Rig testing complete!")
'''
    
    with AugmentBlenderInterface() as blender:
        if blender.connected:
            print("🎬 Testing rig animations...")
            result = blender.execute_blender_code(animation_code)
            
            if result.get('status') == 'success':
                output = result.get('result', {}).get('result', '')
                print("Animation test output:")
                print(output)
                print("\n✅ Animation testing complete!")
            else:
                print(f"❌ Error in animation test: {result.get('message', 'Unknown error')}")
        else:
            print("❌ Could not connect to Blender")

if __name__ == "__main__":
    test_rig_animation()
