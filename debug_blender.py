#!/usr/bin/env python3
"""
Debug Blender connection and check scene
"""

from augment_blender_interface import AugmentBlenderInterface

def debug_blender():
    """Debug what's in the Blender scene"""
    
    debug_code = '''
import bpy

print("=== BLENDER DEBUG INFO ===")
print("Blender version:", bpy.app.version)
print("Scene name:", bpy.context.scene.name)

print("\\n=== ALL OBJECTS ===")
for obj in bpy.data.objects:
    print("Object:", obj.name, "Type:", obj.type, "Location:", obj.location)

print("\\n=== MESH OBJECTS ONLY ===")
mesh_objects = [obj for obj in bpy.data.objects if obj.type == 'MESH']
print("Total mesh objects:", len(mesh_objects))
for obj in mesh_objects:
    print("  ", obj.name, "at", obj.location)

print("\\n=== CAMERA INFO ===")
camera = bpy.data.objects.get("Camera")
if camera:
    print("Camera location:", camera.location)
    print("Camera rotation:", camera.rotation_euler)
else:
    print("No camera found")

print("\\n=== LIGHTS ===")
lights = [obj for obj in bpy.data.objects if obj.type == 'LIGHT']
print("Total lights:", len(lights))
for light in lights:
    print("  ", light.name, "at", light.location)

print("\\n=== MATERIALS ===")
print("Total materials:", len(bpy.data.materials))
for mat in bpy.data.materials:
    print("  ", mat.name)

print("\\n=== DEBUG COMPLETE ===")
'''
    
    with AugmentBlenderInterface() as blender:
        if blender.connected:
            print("🔍 Debugging Blender scene...")
            result = blender.execute_blender_code(debug_code)
            
            if result.get('status') == 'success':
                output = result.get('result', {}).get('result', '')
                print("Debug output:")
                print(output)
            else:
                print(f"❌ Debug error: {result.get('message', 'Unknown error')}")
                print(f"Full result: {result}")
        else:
            print("❌ Could not connect to Blender")

if __name__ == "__main__":
    debug_blender()
