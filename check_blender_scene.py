#!/usr/bin/env python3
"""
Check what's currently in the Blender scene
"""

from augment_blender_interface import AugmentBlenderInterface

def check_scene():
    """Check current Blender scene contents"""
    
    check_code = '''
import bpy

print("=== CURRENT BLENDER SCENE ===")
print("Scene name:", bpy.context.scene.name)

print("\\nAll objects:")
for obj in bpy.data.objects:
    print(f"  {obj.name} ({obj.type}) at {obj.location}")

print("\\nMesh objects only:")
mesh_count = 0
for obj in bpy.data.objects:
    if obj.type == 'MESH':
        mesh_count += 1
        print(f"  {obj.name} at {obj.location}")

print(f"\\nTotal mesh objects: {mesh_count}")

# Check if we have the expected human body parts
expected_parts = ["Human_Body", "Eye_Left", "Eye_Right", "Nose", "Mouth", "Hair", 
                 "Hand_Left", "Hand_Right", "Foot_Left", "Foot_Right"]

print("\\nExpected body parts status:")
for part in expected_parts:
    obj = bpy.data.objects.get(part)
    if obj:
        print(f"  ✅ {part} found at {obj.location}")
    else:
        print(f"  ❌ {part} NOT found")

print("\\n=== END SCENE CHECK ===")
'''
    
    with AugmentBlenderInterface() as blender:
        if blender.connected:
            print("🔍 Checking Blender scene...")
            result = blender.execute_blender_code(check_code)
            
            if result.get('status') == 'success':
                output = result.get('result', {}).get('result', '')
                print("Scene check output:")
                print(output)
            else:
                print(f"❌ Error checking scene: {result}")
        else:
            print("❌ Could not connect to Blender")
            print("💡 Try running the script directly in Blender's Text Editor instead")

if __name__ == "__main__":
    check_scene()
