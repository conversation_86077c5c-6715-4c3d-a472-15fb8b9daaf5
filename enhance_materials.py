#!/usr/bin/env python3
"""
Enhance materials and lighting for the human model
"""

from augment_blender_interface import AugmentBlenderInterface

def enhance_materials():
    """Enhance materials and lighting"""
    
    enhance_code = '''
import bpy
import mathutils
from mathutils import Vector

print("🎨 Enhancing materials and lighting...")

# 1. CREATE BETTER SKIN MATERIAL
def create_realistic_skin():
    """Create realistic skin material"""
    print("👤 Creating realistic skin material...")
    
    # Remove old material if exists
    if "Realistic_Skin" in bpy.data.materials:
        bpy.data.materials.remove(bpy.data.materials["Realistic_Skin"])
    
    # Create new skin material
    skin_mat = bpy.data.materials.new(name="Realistic_Skin")
    skin_mat.use_nodes = True
    nodes = skin_mat.node_tree.nodes
    nodes.clear()
    
    # Create nodes
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    
    # Position nodes
    output.location = (400, 0)
    principled.location = (200, 0)
    
    # Configure principled BSDF for skin
    principled.inputs['Base Color'].default_value = (0.85, 0.6, 0.45, 1.0)
    principled.inputs['Roughness'].default_value = 0.4
    # Check if subsurface inputs exist (Blender version dependent)
    if 'Subsurface' in principled.inputs:
        principled.inputs['Subsurface'].default_value = 0.15
        principled.inputs['Subsurface Color'].default_value = (0.9, 0.4, 0.3, 1.0)
    if 'Subsurface Weight' in principled.inputs:
        principled.inputs['Subsurface Weight'].default_value = 0.15
    
    # Link nodes
    skin_mat.node_tree.links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    # Apply to human body
    human_body = bpy.data.objects.get("Human_Body")
    if human_body:
        if human_body.data.materials:
            human_body.data.materials[0] = skin_mat
        else:
            human_body.data.materials.append(skin_mat)
        print("  ✅ Applied to human body")
    
    return skin_mat

skin_material = create_realistic_skin()

# 2. ENHANCE EYE MATERIALS
def enhance_eyes():
    """Create better eye materials"""
    print("👁️ Enhancing eye materials...")
    
    # Create eye material
    if "Enhanced_Eyes" in bpy.data.materials:
        bpy.data.materials.remove(bpy.data.materials["Enhanced_Eyes"])
    
    eye_mat = bpy.data.materials.new(name="Enhanced_Eyes")
    eye_mat.use_nodes = True
    nodes = eye_mat.node_tree.nodes
    nodes.clear()
    
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    
    # Eye properties - dark brown iris
    principled.inputs['Base Color'].default_value = (0.15, 0.08, 0.04, 1.0)
    principled.inputs['Roughness'].default_value = 0.1
    principled.inputs['Metallic'].default_value = 0.0
    principled.inputs['IOR'].default_value = 1.4
    
    eye_mat.node_tree.links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    # Apply to eyes
    for eye_name in ["Eye_Left", "Eye_Right"]:
        eye = bpy.data.objects.get(eye_name)
        if eye:
            if eye.data.materials:
                eye.data.materials[0] = eye_mat
            else:
                eye.data.materials.append(eye_mat)
    
    print("  ✅ Enhanced eye materials applied")

enhance_eyes()

# 3. ADD HAIR
def add_hair():
    """Add simple hair"""
    print("💇 Adding hair...")
    
    # Remove existing hair if any
    if "Hair" in bpy.data.objects:
        bpy.data.objects.remove(bpy.data.objects["Hair"], do_unlink=True)
    
    # Create hair object using data creation
    bpy.ops.mesh.primitive_uv_sphere_add(radius=0.12)
    hair = bpy.context.object
    hair.name = "Hair"
    hair.location = (0, -0.02, 1.75)
    hair.scale = (1.1, 1.3, 0.9)
    
    # Create hair material
    if "Hair_Material" in bpy.data.materials:
        bpy.data.materials.remove(bpy.data.materials["Hair_Material"])
    
    hair_mat = bpy.data.materials.new(name="Hair_Material")
    hair_mat.use_nodes = True
    nodes = hair_mat.node_tree.nodes
    nodes.clear()
    
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    
    # Hair color (dark brown)
    principled.inputs['Base Color'].default_value = (0.12, 0.08, 0.04, 1.0)
    principled.inputs['Roughness'].default_value = 0.7
    # Check if Sheen exists (Blender version dependent)
    if 'Sheen' in principled.inputs:
        principled.inputs['Sheen'].default_value = 0.8
    
    hair_mat.node_tree.links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    hair.data.materials.append(hair_mat)
    
    print("  ✅ Hair added")

add_hair()

# 4. IMPROVE LIGHTING
def setup_lighting():
    """Set up professional lighting"""
    print("💡 Setting up professional lighting...")
    
    # Remove existing lights except default
    for obj in bpy.data.objects:
        if obj.type == 'LIGHT' and obj.name != 'Light':
            bpy.data.objects.remove(obj, do_unlink=True)
    
    # Enhance default light
    default_light = bpy.data.objects.get('Light')
    if default_light:
        default_light.location = (4, 1, 5)
        default_light.data.energy = 5.0
        default_light.data.type = 'SUN'
        default_light.name = "Key_Light"
    
    # Add fill light using data creation instead of ops
    fill_light_data = bpy.data.lights.new(name="Fill_Light_Data", type='AREA')
    fill_light_data.energy = 2.0
    fill_light_data.size = 3.0
    fill_light_obj = bpy.data.objects.new("Fill_Light", fill_light_data)
    fill_light_obj.location = (-2, 2, 3)
    bpy.context.collection.objects.link(fill_light_obj)

    # Add rim light using data creation
    rim_light_data = bpy.data.lights.new(name="Rim_Light_Data", type='SPOT')
    rim_light_data.energy = 3.0
    rim_light_obj = bpy.data.objects.new("Rim_Light", rim_light_data)
    rim_light_obj.location = (1, -4, 2.5)
    rim_light_obj.rotation_euler = (1.2, 0, 0.3)
    bpy.context.collection.objects.link(rim_light_obj)
    
    print("  ✅ Three-point lighting setup complete")

setup_lighting()

# 5. POSITION CAMERA
def setup_camera():
    """Position camera for better view"""
    print("📷 Positioning camera...")
    
    camera = bpy.data.objects.get("Camera")
    if camera:
        camera.location = (2.5, -2.5, 1.8)
        camera.rotation_euler = (1.1, 0, 0.785)
        
        # Set camera properties
        camera.data.lens = 85  # Portrait lens
        camera.data.clip_start = 0.1
        camera.data.clip_end = 100
        
        print("  ✅ Camera positioned for portrait view")

setup_camera()

# 6. SET RENDER SETTINGS
def setup_render():
    """Configure render settings"""
    print("🎬 Configuring render settings...")
    
    scene = bpy.context.scene
    
    # Set render engine to Cycles for better materials
    scene.render.engine = 'CYCLES'
    
    # Set viewport shading to Material Preview
    for area in bpy.context.screen.areas:
        if area.type == 'VIEW_3D':
            for space in area.spaces:
                if space.type == 'VIEW_3D':
                    space.shading.type = 'MATERIAL'
                    break
    
    print("  ✅ Render settings configured")

setup_render()

print("🎉 MATERIALS AND LIGHTING ENHANCED!")
print("📋 Enhancements made:")
print("  🎨 Realistic skin material with subsurface scattering")
print("  👁️ Enhanced eye materials")
print("  💇 Hair added")
print("  💡 Professional three-point lighting")
print("  📷 Camera positioned for portrait view")
print("  🎬 Render engine set to Cycles")
print("  👀 Viewport set to Material Preview")
print("")
print("💡 Your human model now has:")
print("  ✅ Eyes with pupils")
print("  ✅ Nose and mouth")
print("  ✅ Hands with 5 fingers each")
print("  ✅ Feet with 5 toes each")
print("  ✅ Hair")
print("  ✅ Realistic materials")
print("  ✅ Professional lighting")
print("")
print("🔧 Switch to 'Rendered' viewport shading to see the full effect!")
'''
    
    with AugmentBlenderInterface() as blender:
        if blender.connected:
            print("🎨 Enhancing materials and lighting...")
            result = blender.execute_blender_code(enhance_code)
            
            if result.get('status') == 'success':
                output = result.get('result', {}).get('result', '')
                print("Enhancement output:")
                print(output)
                print("\n✅ Materials and lighting enhanced successfully!")
            else:
                print(f"❌ Error enhancing materials: {result.get('message', 'Unknown error')}")
        else:
            print("❌ Could not connect to Blender")

if __name__ == "__main__":
    enhance_materials()
