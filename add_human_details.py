#!/usr/bin/env python3
"""
Add proper human features: face, eyes, hands with fingers, and other details
"""

from augment_blender_interface import AugmentBlenderInterface

def add_human_details():
    """Add detailed human features"""
    
    details_code = '''
import bpy
import bmesh
import mathutils
from mathutils import Vector
import math

print("👤 Adding detailed human features...")

# Get the human body
human_body = bpy.data.objects.get("Human_Body")
if not human_body:
    print("❌ Human body not found!")
    # List available objects
    for obj in bpy.data.objects:
        if 'human' in obj.name.lower() or 'body' in obj.name.lower():
            print(f"Found potential body: {obj.name}")
            human_body = obj
            break

if not human_body:
    print("❌ No human body found to enhance!")
else:
    print(f"✅ Working with: {human_body.name}")
    
    # 1. ADD EYES
    print("👁️ Adding eyes...")
    
    def create_eyes():
        """Create realistic eyes"""
        # Left eye
        bpy.ops.mesh.primitive_uv_sphere_add(radius=0.05, location=(0.03, 0.08, 1.65))
        left_eye = bpy.context.object
        left_eye.name = "Eye_Left"
        
        # Right eye  
        bpy.ops.mesh.primitive_uv_sphere_add(radius=0.05, location=(-0.03, 0.08, 1.65))
        right_eye = bpy.context.object
        right_eye.name = "Eye_Right"
        
        # Create eye material
        eye_mat = bpy.data.materials.new(name="Eye_Material")
        eye_mat.use_nodes = True
        nodes = eye_mat.node_tree.nodes
        nodes.clear()
        
        output = nodes.new(type='ShaderNodeOutputMaterial')
        principled = nodes.new(type='ShaderNodeBsdfPrincipled')
        
        # Eye color (brown/dark)
        principled.inputs['Base Color'].default_value = (0.2, 0.1, 0.05, 1.0)
        principled.inputs['Roughness'].default_value = 0.1
        
        eye_mat.node_tree.links.new(principled.outputs['BSDF'], output.inputs['Surface'])
        
        # Apply material to both eyes
        left_eye.data.materials.append(eye_mat)
        right_eye.data.materials.append(eye_mat)
        
        print("  ✅ Eyes created")
        return left_eye, right_eye
    
    left_eye, right_eye = create_eyes()
    
    # 2. ADD BASIC FACIAL FEATURES
    print("😊 Adding facial features...")
    
    def add_facial_features():
        """Add nose and mouth"""
        # Nose (simple)
        bpy.ops.mesh.primitive_cube_add(size=0.02, location=(0, 0.09, 1.6))
        nose = bpy.context.object
        nose.name = "Nose"
        nose.scale = (0.5, 1.5, 0.8)
        
        # Mouth area (simple)
        bpy.ops.mesh.primitive_cube_add(size=0.015, location=(0, 0.085, 1.55))
        mouth = bpy.context.object
        mouth.name = "Mouth"
        mouth.scale = (2.0, 0.5, 0.3)
        
        # Create face material
        face_mat = bpy.data.materials.new(name="Face_Material")
        face_mat.use_nodes = True
        nodes = face_mat.node_tree.nodes
        nodes.clear()
        
        output = nodes.new(type='ShaderNodeOutputMaterial')
        principled = nodes.new(type='ShaderNodeBsdfPrincipled')
        
        # Skin tone
        principled.inputs['Base Color'].default_value = (0.85, 0.65, 0.55, 1.0)
        principled.inputs['Roughness'].default_value = 0.4
        
        face_mat.node_tree.links.new(principled.outputs['BSDF'], output.inputs['Surface'])
        
        # Apply material
        nose.data.materials.append(face_mat)
        mouth.data.materials.append(face_mat)
        
        print("  ✅ Basic facial features added")
    
    add_facial_features()
    
    # 3. ADD HANDS WITH FINGERS
    print("✋ Adding hands with fingers...")
    
    def create_hand(side="left"):
        """Create a hand with fingers"""
        # Determine position based on side
        if side == "left":
            hand_pos = (0.7, 0, 1.2)  # Left hand position
            finger_offset = 0.05
        else:
            hand_pos = (-0.7, 0, 1.2)  # Right hand position
            finger_offset = -0.05
        
        # Create palm
        bpy.ops.mesh.primitive_cube_add(size=0.08, location=hand_pos)
        hand = bpy.context.object
        hand.name = f"Hand_{side.title()}"
        hand.scale = (1.2, 0.6, 2.0)
        
        # Create fingers
        fingers = []
        finger_names = ["Thumb", "Index", "Middle", "Ring", "Pinky"]
        finger_positions = [
            (hand_pos[0] + finger_offset*0.8, hand_pos[1] + 0.06, hand_pos[2] + 0.05),  # Thumb
            (hand_pos[0] + finger_offset*0.6, hand_pos[1] - 0.05, hand_pos[2] + 0.12),  # Index
            (hand_pos[0] + finger_offset*0.2, hand_pos[1] - 0.05, hand_pos[2] + 0.15),  # Middle
            (hand_pos[0] - finger_offset*0.2, hand_pos[1] - 0.05, hand_pos[2] + 0.12),  # Ring
            (hand_pos[0] - finger_offset*0.6, hand_pos[1] - 0.05, hand_pos[2] + 0.08),  # Pinky
        ]
        
        for i, (finger_name, finger_pos) in enumerate(zip(finger_names, finger_positions)):
            # Create finger segments
            for segment in range(3):  # 3 segments per finger
                segment_pos = (
                    finger_pos[0],
                    finger_pos[1] - (segment * 0.025),
                    finger_pos[2]
                )
                
                bpy.ops.mesh.primitive_cube_add(size=0.015, location=segment_pos)
                finger_segment = bpy.context.object
                finger_segment.name = f"Finger_{side.title()}_{finger_name}_Seg{segment+1}"
                
                # Scale finger segments
                if finger_name == "Thumb":
                    finger_segment.scale = (0.8, 1.2, 0.8)
                else:
                    finger_segment.scale = (0.6, 1.0, 0.6)
                
                fingers.append(finger_segment)
        
        # Create hand material
        hand_mat = bpy.data.materials.new(name=f"Hand_{side.title()}_Material")
        hand_mat.use_nodes = True
        nodes = hand_mat.node_tree.nodes
        nodes.clear()
        
        output = nodes.new(type='ShaderNodeOutputMaterial')
        principled = nodes.new(type='ShaderNodeBsdfPrincipled')
        
        # Skin tone for hands
        principled.inputs['Base Color'].default_value = (0.8, 0.6, 0.5, 1.0)
        principled.inputs['Roughness'].default_value = 0.5
        
        hand_mat.node_tree.links.new(principled.outputs['BSDF'], output.inputs['Surface'])
        
        # Apply material to hand and fingers
        hand.data.materials.append(hand_mat)
        for finger in fingers:
            finger.data.materials.append(hand_mat)
        
        print(f"  ✅ {side.title()} hand with fingers created")
        return hand, fingers
    
    # Create both hands
    left_hand, left_fingers = create_hand("left")
    right_hand, right_fingers = create_hand("right")
    
    # 4. ADD FEET WITH BASIC TOES
    print("🦶 Adding feet with toes...")
    
    def create_foot(side="left"):
        """Create a foot with toes"""
        if side == "left":
            foot_pos = (0.15, 0, 0.05)
        else:
            foot_pos = (-0.15, 0, 0.05)
        
        # Create foot
        bpy.ops.mesh.primitive_cube_add(size=0.1, location=foot_pos)
        foot = bpy.context.object
        foot.name = f"Foot_{side.title()}"
        foot.scale = (1.0, 2.5, 0.5)
        
        # Create toes
        for i in range(5):
            toe_pos = (
                foot_pos[0] + (i - 2) * 0.03,
                foot_pos[1] - 0.15,
                foot_pos[2] + 0.02
            )
            
            bpy.ops.mesh.primitive_cube_add(size=0.02, location=toe_pos)
            toe = bpy.context.object
            toe.name = f"Toe_{side.title()}_{i+1}"
            toe.scale = (0.8, 1.5, 0.6)
        
        print(f"  ✅ {side.title()} foot with toes created")
    
    create_foot("left")
    create_foot("right")
    
    # 5. ORGANIZE ALL NEW OBJECTS
    print("📁 Organizing new body parts...")
    
    # Create a collection for detailed body parts
    if "Body_Details" not in bpy.data.collections:
        detail_collection = bpy.data.collections.new("Body_Details")
        bpy.context.scene.collection.children.link(detail_collection)
    else:
        detail_collection = bpy.data.collections["Body_Details"]
    
    # Move new objects to detail collection
    detail_objects = []
    for obj in bpy.data.objects:
        if any(keyword in obj.name for keyword in ["Eye_", "Nose", "Mouth", "Hand_", "Finger_", "Foot_", "Toe_"]):
            if obj.name not in detail_collection.objects:
                detail_collection.objects.link(obj)
                if obj.name in bpy.context.scene.collection.objects:
                    bpy.context.scene.collection.objects.unlink(obj)
                detail_objects.append(obj.name)
    
    print(f"  ✅ Organized {len(detail_objects)} detail objects")

print("🎉 HUMAN DETAILS COMPLETE!")
print("📋 Added features:")
print("  👁️ Eyes (left and right)")
print("  👃 Nose")
print("  👄 Mouth")
print("  ✋ Hands with 5 fingers each (left and right)")
print("  🦶 Feet with 5 toes each (left and right)")
print("  🎨 Appropriate materials for each part")
print("  📁 Organized in Body_Details collection")
print("")
print("💡 TIP: You can now see a much more detailed human figure!")
print("🔧 For even more realism, you can:")
print("  - Sculpt the features in Sculpting mode")
print("  - Add hair with particle systems")
print("  - Create more detailed facial expressions")
print("  - Add clothing and accessories")
'''
    
    with AugmentBlenderInterface() as blender:
        if blender.connected:
            print("👤 Adding detailed human features...")
            result = blender.execute_blender_code(details_code)
            
            if result.get('status') == 'success':
                output = result.get('result', {}).get('result', '')
                print("Human details output:")
                print(output)
                print("\n✅ Human details added successfully!")
            else:
                print(f"❌ Error adding details: {result.get('message', 'Unknown error')}")
        else:
            print("❌ Could not connect to Blender")

if __name__ == "__main__":
    add_human_details()
