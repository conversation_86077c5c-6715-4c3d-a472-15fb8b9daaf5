import bpy

print("=== CHECKING BLENDER SCENE ===")

print("\nAll objects in scene:")
for obj in bpy.data.objects:
    print(f"  {obj.name} ({obj.type}) at location {obj.location}")

print("\nMesh objects only:")
mesh_objects = [obj for obj in bpy.data.objects if obj.type == 'MESH']
print(f"Total mesh objects: {len(mesh_objects)}")

for obj in mesh_objects:
    print(f"  {obj.name} at {obj.location}")

print("\nCamera info:")
camera = bpy.data.objects.get("Camera")
if camera:
    print(f"  Camera at {camera.location}")
    print(f"  Camera rotation: {camera.rotation_euler}")
else:
    print("  No camera found")

print("\nLights:")
lights = [obj for obj in bpy.data.objects if obj.type == 'LIGHT']
print(f"Total lights: {len(lights)}")
for light in lights:
    print(f"  {light.name} at {light.location}")

print("\n=== SCENE CHECK COMPLETE ===")
