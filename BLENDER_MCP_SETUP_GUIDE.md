# Blender MCP Setup Guide
## Real-time AI-Blender Communication

This guide will help you set up **BlenderMCP** for real-time communication between AI assistants and <PERSON><PERSON>der for 3D animation workflows.

## What You'll Get

✅ **Real-time two-way communication** between AI and Blender  
✅ **Object manipulation** - create, modify, delete 3D objects  
✅ **Material control** - apply and modify materials/colors  
✅ **Scene inspection** - get detailed scene information  
✅ **Code execution** - run Python scripts in Blender  
✅ **Asset integration** - download models from Poly Haven and Sketchfab  
✅ **AI-generated 3D models** through Hyper3D Rodin  

## Prerequisites

- **Blender 3.0 or newer** (download from [blender.org](https://www.blender.org/download/))
- **Python 3.10 or newer**
- **uv package manager** ✅ (already installed)
- **Claude Desktop** or compatible AI client

## Step 1: Install Blender

1. Download Blender from [blender.org](https://www.blender.org/download/)
2. Install Blender following the standard installation process
3. Launch Blender to verify it works

## Step 2: Install the Blender Addon

1. **Download the addon**: The `addon.py` file is already in your project directory
2. **Open Blender**
3. **Go to Edit > Preferences > Add-ons**
4. **Click "Install..."** and select the `addon.py` file from your project directory
5. **Enable the addon** by checking the box next to "Interface: Blender MCP"
6. **Save Preferences** (Blender should remember this setting)

## Step 3: Configure the MCP Server

### For Claude Desktop:

1. **Open Claude Desktop**
2. **Go to Settings** (gear icon)
3. **Navigate to Developer > Edit Config**
4. **Add the following configuration** to your `claude_desktop_config.json`:

```json
{
    "mcpServers": {
        "blender": {
            "command": "uvx",
            "args": [
                "blender-mcp"
            ]
        }
    }
}
```

5. **Save the file** and **restart Claude Desktop**

### For Cursor (Alternative):

1. **Open Cursor**
2. **Go to Settings > MCP**
3. **Add a new server** with these settings:

```json
{
    "mcpServers": {
        "blender": {
            "command": "uvx",
            "args": [
                "blender-mcp"
            ]
        }
    }
}
```

## Step 4: Start the Connection

### In Blender:
1. **Open Blender**
2. **Press N** to open the sidebar (if not visible)
3. **Find the "BlenderMCP" tab** in the sidebar
4. **Configure optional features**:
   - ☐ Use assets from Poly Haven (for textures, HDRIs, models)
   - ☐ Use Hyper3D Rodin 3D model generation (AI-generated models)
   - ☐ Use assets from Sketchfab (requires API key)
5. **Click "Connect to MCP server"**
6. **Verify** you see "Running on port 9876"

### In Claude Desktop:
1. **Open Claude Desktop**
2. **Look for the hammer icon** 🔨 in the chat interface
3. **You should see "Blender MCP" tools available**
4. **Test the connection** by asking: "Can you get information about my current Blender scene?"

## Step 5: Test the Setup

Try these example commands in Claude:

1. **"Get information about my current Blender scene"**
2. **"Create a cube and place it at position (2, 0, 0)"**
3. **"Take a screenshot of the current viewport"**
4. **"Create a simple material and apply it to the default cube"**

## Troubleshooting

### Connection Issues:
- Make sure Blender addon server is running (green status in BlenderMCP panel)
- Restart both Claude Desktop and Blender
- Check that port 9876 is not blocked by firewall

### Addon Installation Issues:
- Make sure you're using Blender 3.0 or newer
- Try installing the addon as Administrator/with elevated permissions
- Check Blender's console for error messages

### MCP Server Issues:
- Verify `uv` is installed: run `uv --version` in terminal
- Make sure the MCP configuration is valid JSON
- Only run one MCP client at a time (Claude OR Cursor, not both)

## Advanced Features

### Poly Haven Integration:
- Enable in BlenderMCP panel
- Download high-quality textures, HDRIs, and models
- No API key required

### Hyper3D Rodin (AI Model Generation):
- Enable in BlenderMCP panel
- Use free trial key or get your own from hyper3d.ai
- Generate 3D models from text prompts

### Sketchfab Integration:
- Enable in BlenderMCP panel
- Requires free Sketchfab API key
- Access thousands of downloadable 3D models

## Example Workflows

1. **"Create a low poly dungeon scene with a dragon guarding treasure"**
2. **"Set up studio lighting for product visualization"**
3. **"Create a beach scene with realistic materials and HDRI lighting"**
4. **"Generate a 3D model of a garden gnome and place it in the scene"**

## Next Steps

Once everything is working:
- Experiment with different animation commands
- Try procedural modeling with Python scripts
- Explore asset downloading from various sources
- Create complex scenes with AI assistance

## Support

If you encounter issues:
1. Check the BlenderMCP GitHub repository for updates
2. Review Blender's console for error messages
3. Ensure all prerequisites are properly installed
4. Try the troubleshooting steps above

Happy animating! 🎬✨
