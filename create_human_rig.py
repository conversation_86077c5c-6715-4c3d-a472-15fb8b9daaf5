#!/usr/bin/env python3
"""
Create a human character with animation rig in Blender
"""

from augment_blender_interface import AugmentBlenderInterface

def create_human_with_rig():
    """Create a human body with animation rig"""
    
    human_rig_code = '''
import bpy

print("🧑 Starting human character creation...")

# Enable Rigify addon if not already enabled
try:
    if "rigify" not in bpy.context.preferences.addons:
        bpy.ops.preferences.addon_enable(module="rigify")
        print("✅ Rigify addon enabled")
    else:
        print("✅ Rigify addon already enabled")
except:
    print("⚠️ Rigify addon not available, creating basic armature instead")

# Clear existing objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False, confirm=False)
print("🧹 Scene cleared")

# Try to add Rigify human metarig, fallback to basic armature
try:
    bpy.ops.object.armature_human_metarig_add()
    metarig = bpy.context.active_object
    metarig.name = "Human_Metarig"
    print(f"✅ Created Rigify metarig: {metarig.name}")
    
    # Generate the final rig from metarig
    bpy.ops.pose.rigify_generate()
    final_rig = bpy.context.active_object
    if final_rig:
        final_rig.name = "Human_Rig"
        print(f"✅ Generated final rig: {final_rig.name}")
    
except:
    print("⚠️ Rigify not available, creating basic armature...")
    # Create basic armature manually
    bpy.ops.object.armature_add(location=(0, 0, 0))
    armature = bpy.context.active_object
    armature.name = "Basic_Human_Rig"
    
    # Enter edit mode to add bones
    bpy.ops.object.mode_set(mode='EDIT')
    
    # Get the armature data
    arm_data = armature.data
    
    # Clear default bone
    bpy.ops.armature.select_all(action='SELECT')
    bpy.ops.armature.delete()
    
    # Create spine bones
    spine_bone = arm_data.edit_bones.new('Spine')
    spine_bone.head = (0, 0, 0.5)
    spine_bone.tail = (0, 0, 1.5)
    
    chest_bone = arm_data.edit_bones.new('Chest')
    chest_bone.head = (0, 0, 1.5)
    chest_bone.tail = (0, 0, 1.8)
    chest_bone.parent = spine_bone
    
    # Create head/neck
    neck_bone = arm_data.edit_bones.new('Neck')
    neck_bone.head = (0, 0, 1.8)
    neck_bone.tail = (0, 0, 1.9)
    neck_bone.parent = chest_bone
    
    head_bone = arm_data.edit_bones.new('Head')
    head_bone.head = (0, 0, 1.9)
    head_bone.tail = (0, 0, 2.1)
    head_bone.parent = neck_bone
    
    # Create arms
    l_shoulder = arm_data.edit_bones.new('L_Shoulder')
    l_shoulder.head = (0.2, 0, 1.7)
    l_shoulder.tail = (0.5, 0, 1.7)
    l_shoulder.parent = chest_bone
    
    l_upper_arm = arm_data.edit_bones.new('L_UpperArm')
    l_upper_arm.head = (0.5, 0, 1.7)
    l_upper_arm.tail = (0.8, 0, 1.4)
    l_upper_arm.parent = l_shoulder
    
    l_forearm = arm_data.edit_bones.new('L_Forearm')
    l_forearm.head = (0.8, 0, 1.4)
    l_forearm.tail = (1.1, 0, 1.1)
    l_forearm.parent = l_upper_arm
    
    l_hand = arm_data.edit_bones.new('L_Hand')
    l_hand.head = (1.1, 0, 1.1)
    l_hand.tail = (1.3, 0, 1.1)
    l_hand.parent = l_forearm
    
    # Mirror for right arm
    r_shoulder = arm_data.edit_bones.new('R_Shoulder')
    r_shoulder.head = (-0.2, 0, 1.7)
    r_shoulder.tail = (-0.5, 0, 1.7)
    r_shoulder.parent = chest_bone
    
    r_upper_arm = arm_data.edit_bones.new('R_UpperArm')
    r_upper_arm.head = (-0.5, 0, 1.7)
    r_upper_arm.tail = (-0.8, 0, 1.4)
    r_upper_arm.parent = r_shoulder
    
    r_forearm = arm_data.edit_bones.new('R_Forearm')
    r_forearm.head = (-0.8, 0, 1.4)
    r_forearm.tail = (-1.1, 0, 1.1)
    r_forearm.parent = r_upper_arm
    
    r_hand = arm_data.edit_bones.new('R_Hand')
    r_hand.head = (-1.1, 0, 1.1)
    r_hand.tail = (-1.3, 0, 1.1)
    r_hand.parent = r_forearm
    
    # Create legs
    l_thigh = arm_data.edit_bones.new('L_Thigh')
    l_thigh.head = (0.1, 0, 0.5)
    l_thigh.tail = (0.1, 0, 0.0)
    l_thigh.parent = spine_bone
    
    l_shin = arm_data.edit_bones.new('L_Shin')
    l_shin.head = (0.1, 0, 0.0)
    l_shin.tail = (0.1, 0, -0.5)
    l_shin.parent = l_thigh
    
    l_foot = arm_data.edit_bones.new('L_Foot')
    l_foot.head = (0.1, 0, -0.5)
    l_foot.tail = (0.1, 0.2, -0.5)
    l_foot.parent = l_shin
    
    # Mirror for right leg
    r_thigh = arm_data.edit_bones.new('R_Thigh')
    r_thigh.head = (-0.1, 0, 0.5)
    r_thigh.tail = (-0.1, 0, 0.0)
    r_thigh.parent = spine_bone
    
    r_shin = arm_data.edit_bones.new('R_Shin')
    r_shin.head = (-0.1, 0, 0.0)
    r_shin.tail = (-0.1, 0, -0.5)
    r_shin.parent = r_thigh
    
    r_foot = arm_data.edit_bones.new('R_Foot')
    r_foot.head = (-0.1, 0, -0.5)
    r_foot.tail = (-0.1, 0.2, -0.5)
    r_foot.parent = r_shin
    
    bpy.ops.object.mode_set(mode='OBJECT')
    final_rig = armature
    print("✅ Created basic human armature with bones")

# Create a human body mesh
bpy.ops.mesh.primitive_cube_add(location=(0, 0, 1))
body_mesh = bpy.context.active_object
body_mesh.name = "Human_Body"

# Make it more human-like
bpy.context.view_layer.objects.active = body_mesh
bpy.ops.object.mode_set(mode='EDIT')
bpy.ops.mesh.subdivide(number_cuts=2)
bpy.ops.object.mode_set(mode='OBJECT')

# Scale to human proportions
body_mesh.scale = (0.4, 0.2, 0.9)
bpy.ops.object.transform_apply(location=False, rotation=False, scale=True)

# Create skin material
mat = bpy.data.materials.new(name="Human_Skin")
mat.use_nodes = True
principled = mat.node_tree.nodes["Principled BSDF"]
principled.inputs[0].default_value = (0.8, 0.6, 0.5, 1.0)  # Skin color
principled.inputs[7].default_value = 0.2  # Roughness
body_mesh.data.materials.append(mat)
print("✅ Created human body mesh with skin material")

# Parent body to rig
if final_rig and body_mesh:
    bpy.ops.object.select_all(action='DESELECT')
    body_mesh.select_set(True)
    final_rig.select_set(True)
    bpy.context.view_layer.objects.active = final_rig
    
    try:
        bpy.ops.object.parent_set(type='ARMATURE_AUTO')
        print("✅ Body parented to rig with automatic weights")
    except:
        bpy.ops.object.parent_set(type='ARMATURE')
        print("✅ Body parented to rig (manual weights needed)")

# Set up scene
bpy.context.scene.frame_start = 1
bpy.context.scene.frame_end = 250

# Add lighting
bpy.ops.object.light_add(type='SUN', location=(5, 5, 10))
sun = bpy.context.active_object
sun.name = "Sun_Light"
sun.data.energy = 3

# Add camera
bpy.ops.object.camera_add(location=(4, -4, 3))
camera = bpy.context.active_object
camera.name = "Character_Camera"

# Point camera at character
import mathutils
direction = mathutils.Vector((0, 0, 1)) - camera.location
camera.rotation_euler = direction.to_track_quat('-Z', 'Y').to_euler()
bpy.context.scene.camera = camera

print("🎉 Human character with rig created successfully!")
print("📋 Ready for character animation!")
'''
    
    with AugmentBlenderInterface() as blender:
        if blender.connected:
            print("🧑 Creating human character with animation rig...")
            result = blender.execute_blender_code(human_rig_code)
            
            if result.get('status') == 'success':
                output = result.get('result', {}).get('result', '')
                print("Creation output:")
                print(output)
                
                # Get scene info
                scene = blender.get_scene_info()
                if scene.get('status') == 'success':
                    objects = scene['result']['objects']
                    print(f"\n📋 Scene now contains {len(objects)} objects:")
                    for obj in objects:
                        print(f"   - {obj['name']} ({obj['type']})")
                
                print("\n🎉 Human character with animation rig created!")
                print("🎬 You can now:")
                print("   - Switch to Pose mode to animate the character")
                print("   - Select bones and create keyframes")
                print("   - Use the timeline to create walk cycles")
                print("   - Render animations from the camera view")
            else:
                print(f"❌ Error: {result.get('message', 'Unknown error')}")
        else:
            print("❌ Could not connect to Blender")

if __name__ == "__main__":
    create_human_with_rig()
