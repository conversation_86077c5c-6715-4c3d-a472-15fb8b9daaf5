#!/usr/bin/env python3
"""
Complete enhancement of the human model:
1. Add realistic skin materials
2. Set up proper lighting
3. Test the rig with animation
4. Add more detail and features
"""

from augment_blender_interface import AugmentBlenderInterface

def enhance_human_complete():
    """Complete enhancement of the human model"""
    
    enhancement_code = '''
import bpy
import bmesh
import mathutils
from mathutils import Vector, Color
import math

print("🚀 Starting complete human enhancement...")

# Get the human body and rig
human_body = bpy.data.objects.get("Human_Body")
human_rig = bpy.data.objects.get("Human_Rig") or bpy.data.objects.get("RIG-Human_Metarig")

if not human_body:
    print("❌ Human body not found!")
    # List available objects
    print("Available objects:")
    for obj in bpy.data.objects:
        print(f"  - {obj.name} ({obj.type})")
else:
    print(f"✅ Found human body: {human_body.name}")
    if human_rig:
        print(f"✅ Found rig: {human_rig.name}")

# 1. CREATE REALISTIC SKIN MATERIALS
print("🎨 Creating realistic skin materials...")

def create_skin_material():
    """Create a realistic skin material"""
    # Remove existing materials
    if human_body.data.materials:
        human_body.data.materials.clear()
    
    # Create new skin material
    skin_mat = bpy.data.materials.new(name="Human_Skin")
    skin_mat.use_nodes = True
    nodes = skin_mat.node_tree.nodes
    links = skin_mat.node_tree.links
    
    # Clear default nodes
    nodes.clear()
    
    # Add nodes for realistic skin
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    
    # Set skin color and properties (compatible with all Blender versions)
    principled.inputs['Base Color'].default_value = (0.8, 0.6, 0.5, 1.0)  # Skin tone

    # Try to set subsurface if available (newer Blender versions)
    try:
        principled.inputs['Subsurface'].default_value = 0.1
        principled.inputs['Subsurface Color'].default_value = (0.9, 0.7, 0.6, 1.0)
    except KeyError:
        print("  Note: Subsurface not available in this Blender version")

    principled.inputs['Roughness'].default_value = 0.4

    # Try to set specular (older versions) or IOR (newer versions)
    try:
        principled.inputs['Specular'].default_value = 0.3
    except KeyError:
        try:
            principled.inputs['IOR'].default_value = 1.4  # Skin IOR
        except KeyError:
            print("  Note: Neither Specular nor IOR available")
    
    # Connect nodes
    links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    # Position nodes
    output.location = (300, 0)
    principled.location = (0, 0)
    
    # Assign material to body
    human_body.data.materials.append(skin_mat)
    print("✅ Created realistic skin material")

if human_body:
    create_skin_material()

# 2. SET UP PROFESSIONAL LIGHTING
print("💡 Setting up professional lighting...")

def setup_lighting():
    """Set up professional 3-point lighting"""
    
    # Remove existing lights except default
    for obj in bpy.data.objects:
        if obj.type == 'LIGHT' and obj.name != 'Light':
            bpy.data.objects.remove(obj, do_unlink=True)
    
    # Key light (main light)
    bpy.ops.object.light_add(type='SUN', location=(5, -5, 8))
    key_light = bpy.context.object
    key_light.name = "Key_Light"
    key_light.data.energy = 3.0
    key_light.data.color = (1.0, 0.95, 0.9)  # Warm white
    
    # Fill light (softer, opposite side)
    bpy.ops.object.light_add(type='AREA', location=(-3, -2, 4))
    fill_light = bpy.context.object
    fill_light.name = "Fill_Light"
    fill_light.data.energy = 1.5
    fill_light.data.size = 4.0
    fill_light.data.color = (0.9, 0.95, 1.0)  # Cool white
    
    # Rim light (back light for edge definition)
    bpy.ops.object.light_add(type='SPOT', location=(0, 5, 6))
    rim_light = bpy.context.object
    rim_light.name = "Rim_Light"
    rim_light.data.energy = 2.0
    rim_light.data.spot_size = math.radians(45)
    rim_light.data.color = (1.0, 1.0, 0.95)
    
    # Point rim light at the human
    if human_body:
        constraint = rim_light.constraints.new(type='TRACK_TO')
        constraint.target = human_body
        constraint.track_axis = 'TRACK_NEGATIVE_Z'
        constraint.up_axis = 'UP_Y'
    
    print("✅ Set up 3-point lighting system")

setup_lighting()

# 3. ADD DETAIL TO THE MODEL
print("🔧 Adding detail to the model...")

def add_model_details():
    """Add more detail to the human model"""
    if not human_body:
        return
        
    # Enter edit mode
    bpy.context.view_layer.objects.active = human_body
    bpy.ops.object.mode_set(mode='EDIT')
    
    # Add subdivision for smoother appearance
    bpy.ops.mesh.select_all(action='SELECT')
    bpy.ops.mesh.subdivide(number_cuts=1, smoothness=0.5)
    
    # Smooth the mesh
    bpy.ops.mesh.faces_shade_smooth()
    
    # Exit edit mode
    bpy.ops.object.mode_set(mode='OBJECT')
    
    # Add subdivision surface modifier for even smoother appearance
    if not any(mod.type == 'SUBSURF' for mod in human_body.modifiers):
        subsurf = human_body.modifiers.new(name="Subdivision", type='SUBSURF')
        subsurf.levels = 1
        subsurf.render_levels = 2
    
    print("✅ Added detail and smoothing to model")

if human_body:
    add_model_details()

print("✅ Enhancement complete!")
'''
    
    with AugmentBlenderInterface() as blender:
        if blender.connected:
            print("🚀 Starting complete human enhancement...")
            result = blender.execute_blender_code(enhancement_code)
            
            if result.get('status') == 'success':
                output = result.get('result', {}).get('result', '')
                print("Enhancement output:")
                print(output)
                print("\n✅ Phase 1 complete! Now adding animation test...")
            else:
                print(f"❌ Error in enhancement: {result.get('message', 'Unknown error')}")
        else:
            print("❌ Could not connect to Blender")

if __name__ == "__main__":
    enhance_human_complete()
