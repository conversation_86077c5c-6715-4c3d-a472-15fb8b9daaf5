#!/usr/bin/env python3
"""
Fix the human body by properly joining and positioning all parts
"""

from augment_blender_interface import AugmentBlenderInterface

def fix_body_assembly():
    """Join all body parts into a cohesive human figure"""
    
    fix_code = '''
import bpy
import bmesh
from mathutils import Vector

print("🔧 Fixing human body assembly...")

# 1. FIRST, LET'S SEE WHAT WE HAVE
def list_objects():
    """List all objects in the scene"""
    print("📋 Current objects in scene:")
    for obj in bpy.data.objects:
        if obj.type == 'MESH':
            print(f"  🔹 {obj.name} at {obj.location}")
    print()

list_objects()

# 2. POSITION BODY PARTS CORRECTLY
def position_body_parts():
    """Position all body parts relative to the main body"""
    print("📍 Positioning body parts correctly...")
    
    # Get the main human body
    human_body = bpy.data.objects.get("Human_Body")
    if not human_body:
        print("❌ Main human body not found!")
        return
    
    print(f"✅ Found main body: {human_body.name}")
    
    # Position eyes correctly
    left_eye = bpy.data.objects.get("Eye_Left")
    right_eye = bpy.data.objects.get("Eye_Right")
    
    if left_eye:
        left_eye.location = (0.03, 0.08, 1.65)
        print("  👁️ Positioned left eye")
    if right_eye:
        right_eye.location = (-0.03, 0.08, 1.65)
        print("  👁️ Positioned right eye")
    
    # Position facial features
    nose = bpy.data.objects.get("Nose")
    if nose:
        nose.location = (0, 0.09, 1.6)
        print("  👃 Positioned nose")
    
    mouth = bpy.data.objects.get("Mouth")
    if mouth:
        mouth.location = (0, 0.085, 1.55)
        print("  👄 Positioned mouth")
    
    # Position hair
    hair = bpy.data.objects.get("Hair")
    if hair:
        hair.location = (0, -0.02, 1.75)
        print("  💇 Positioned hair")
    
    # Position hands closer to arms
    left_hand = bpy.data.objects.get("Hand_Left")
    right_hand = bpy.data.objects.get("Hand_Right")
    
    if left_hand:
        left_hand.location = (0.25, 0, 1.2)  # Closer to body
        print("  ✋ Positioned left hand")
    if right_hand:
        right_hand.location = (-0.25, 0, 1.2)  # Closer to body
        print("  ✋ Positioned right hand")
    
    # Position fingers relative to hands
    finger_positions = {
        "left": {
            "Thumb": (0.28, 0.06, 1.25),
            "Index": (0.26, -0.05, 1.32),
            "Middle": (0.24, -0.05, 1.35),
            "Ring": (0.22, -0.05, 1.32),
            "Pinky": (0.20, -0.05, 1.28)
        },
        "right": {
            "Thumb": (-0.28, 0.06, 1.25),
            "Index": (-0.26, -0.05, 1.32),
            "Middle": (-0.24, -0.05, 1.35),
            "Ring": (-0.22, -0.05, 1.32),
            "Pinky": (-0.20, -0.05, 1.28)
        }
    }
    
    # Position fingers
    for side in ["left", "right"]:
        for finger_name, base_pos in finger_positions[side].items():
            for segment in range(1, 4):  # 3 segments per finger
                finger_obj_name = f"Finger_{side.title()}_{finger_name}_Seg{segment}"
                finger_obj = bpy.data.objects.get(finger_obj_name)
                if finger_obj:
                    # Position each segment slightly forward from the base
                    segment_offset = (segment - 1) * 0.025
                    finger_obj.location = (
                        base_pos[0],
                        base_pos[1] - segment_offset,
                        base_pos[2]
                    )
    
    print("  🖐️ Positioned all fingers")
    
    # Position feet
    left_foot = bpy.data.objects.get("Foot_Left")
    right_foot = bpy.data.objects.get("Foot_Right")
    
    if left_foot:
        left_foot.location = (0.1, 0, 0.05)
        print("  🦶 Positioned left foot")
    if right_foot:
        right_foot.location = (-0.1, 0, 0.05)
        print("  🦶 Positioned right foot")
    
    # Position toes
    toe_positions = {
        "left": [(0.1 + (i-2)*0.02, -0.15, 0.07) for i in range(5)],
        "right": [(-0.1 + (i-2)*0.02, -0.15, 0.07) for i in range(5)]
    }
    
    for side in ["left", "right"]:
        for i, toe_pos in enumerate(toe_positions[side]):
            toe_name = f"Toe_{side.title()}_{i+1}"
            toe_obj = bpy.data.objects.get(toe_name)
            if toe_obj:
                toe_obj.location = toe_pos
    
    print("  🦶 Positioned all toes")

position_body_parts()

# 3. JOIN SIMILAR PARTS TOGETHER
def join_body_parts():
    """Join related parts together for better organization"""
    print("🔗 Joining related body parts...")
    
    # Clear selection
    bpy.ops.object.select_all(action='DESELECT')
    
    # Join all finger segments to their respective hands
    for side in ["Left", "Right"]:
        hand_name = f"Hand_{side}"
        hand_obj = bpy.data.objects.get(hand_name)
        
        if hand_obj:
            # Select the hand first
            hand_obj.select_set(True)
            bpy.context.view_layer.objects.active = hand_obj
            
            # Select all finger segments for this hand
            finger_count = 0
            for obj in bpy.data.objects:
                if obj.name.startswith(f"Finger_{side}_"):
                    obj.select_set(True)
                    finger_count += 1
            
            if finger_count > 0:
                # Join fingers to hand
                bpy.ops.object.join()
                print(f"  ✅ Joined {finger_count} finger segments to {hand_name}")
            
            # Clear selection
            bpy.ops.object.select_all(action='DESELECT')
    
    # Join all toe segments to their respective feet
    for side in ["Left", "Right"]:
        foot_name = f"Foot_{side}"
        foot_obj = bpy.data.objects.get(foot_name)
        
        if foot_obj:
            # Select the foot first
            foot_obj.select_set(True)
            bpy.context.view_layer.objects.active = foot_obj
            
            # Select all toe segments for this foot
            toe_count = 0
            for obj in bpy.data.objects:
                if obj.name.startswith(f"Toe_{side}_"):
                    obj.select_set(True)
                    toe_count += 1
            
            if toe_count > 0:
                # Join toes to foot
                bpy.ops.object.join()
                print(f"  ✅ Joined {toe_count} toes to {foot_name}")
            
            # Clear selection
            bpy.ops.object.select_all(action='DESELECT')

join_body_parts()

# 4. IMPROVE CAMERA AND LIGHTING FOR FULL BODY VIEW
def setup_full_body_view():
    """Set up camera and lighting to show the complete figure"""
    print("📷 Setting up full body view...")
    
    # Position camera to show full body
    camera = bpy.data.objects.get("Camera")
    if camera:
        camera.location = (3, -3, 1.5)  # Further back to see full body
        camera.rotation_euler = (1.1, 0, 0.785)
        camera.data.lens = 35  # Wider lens for full body
        print("  ✅ Camera positioned for full body view")
    
    # Adjust key light
    key_light = bpy.data.objects.get("Key_Light") or bpy.data.objects.get("Light")
    if key_light:
        key_light.location = (4, 2, 4)
        key_light.data.energy = 8.0
        print("  ✅ Key light adjusted")

setup_full_body_view()

# 5. CREATE A SIMPLE GROUND PLANE
def add_ground():
    """Add a ground plane for reference"""
    print("🌍 Adding ground plane...")
    
    # Remove existing ground if any
    if "Ground" in bpy.data.objects:
        bpy.data.objects.remove(bpy.data.objects["Ground"], do_unlink=True)
    
    # Create ground plane
    bpy.ops.mesh.primitive_plane_add(size=10, location=(0, 0, -0.1))
    ground = bpy.context.object
    ground.name = "Ground"
    
    # Create ground material
    ground_mat = bpy.data.materials.new(name="Ground_Material")
    ground_mat.use_nodes = True
    nodes = ground_mat.node_tree.nodes
    nodes.clear()
    
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    
    # Light gray ground
    principled.inputs['Base Color'].default_value = (0.8, 0.8, 0.8, 1.0)
    principled.inputs['Roughness'].default_value = 0.9
    
    ground_mat.node_tree.links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    ground.data.materials.append(ground_mat)
    
    print("  ✅ Ground plane added")

add_ground()

print("🎉 BODY ASSEMBLY FIXED!")
print("📋 Fixes applied:")
print("  📍 All body parts positioned correctly")
print("  🔗 Fingers joined to hands")
print("  🔗 Toes joined to feet")
print("  📷 Camera adjusted for full body view")
print("  🌍 Ground plane added for reference")
print("")
print("💡 Now press F12 again to see the complete human figure!")
print("🔧 You should now see:")
print("  👤 Complete human body with all parts in place")
print("  ✋ Hands with fingers attached")
print("  🦶 Feet with toes attached")
print("  👁️ Eyes, nose, mouth in correct positions")
print("  💇 Hair on the head")
'''
    
    with AugmentBlenderInterface() as blender:
        if blender.connected:
            print("🔧 Fixing human body assembly...")
            result = blender.execute_blender_code(fix_code)
            
            if result.get('status') == 'success':
                output = result.get('result', {}).get('result', '')
                print("Body assembly fix output:")
                print(output)
                print("\n✅ Body assembly fixed successfully!")
            else:
                print(f"❌ Error fixing body: {result.get('message', 'Unknown error')}")
        else:
            print("❌ Could not connect to Blender")

if __name__ == "__main__":
    fix_body_assembly()
