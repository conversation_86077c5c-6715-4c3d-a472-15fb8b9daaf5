#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run directly in Blender's Text Editor to fix the human body
Copy and paste this into Blender's Text Editor and click Run Script
"""

import bpy
from mathutils import Vector

print("🔧 Fixing human body assembly...")

# 1. List current objects to see what we have
print("📋 Current objects in scene:")
mesh_objects = []
for obj in bpy.data.objects:
    if obj.type == 'MESH':
        mesh_objects.append(obj)
        print(f"  🔹 {obj.name} at {obj.location}")

print(f"Total mesh objects: {len(mesh_objects)}")

# 2. Position body parts correctly
print("📍 Positioning body parts...")

# Define correct positions for body parts
positions = {
    "Human_Body": (0, 0, 1.0),  # Main body at center
    "Eye_Left": (0.03, 0.08, 1.65),
    "Eye_Right": (-0.03, 0.08, 1.65),
    "Nose": (0, 0.09, 1.6),
    "Mouth": (0, 0.085, 1.55),
    "Hair": (0, -0.02, 1.75),
    "Hand_Left": (0.25, 0, 1.2),
    "Hand_Right": (-0.25, 0, 1.2),
    "Foot_Left": (0.1, 0, 0.05),
    "Foot_Right": (-0.1, 0, 0.05)
}

# Apply positions
positioned_count = 0
for obj_name, pos in positions.items():
    obj = bpy.data.objects.get(obj_name)
    if obj:
        obj.location = Vector(pos)
        positioned_count += 1
        print(f"  ✅ Positioned {obj_name}")
    else:
        print(f"  ❌ Object {obj_name} not found")

print(f"Positioned {positioned_count} objects")

# 3. Position fingers relative to hands
print("🖐️ Positioning fingers...")

finger_positions = {
    "left": {
        "Thumb": (0.28, 0.06, 1.25),
        "Index": (0.26, -0.05, 1.32),
        "Middle": (0.24, -0.05, 1.35),
        "Ring": (0.22, -0.05, 1.32),
        "Pinky": (0.20, -0.05, 1.28)
    },
    "right": {
        "Thumb": (-0.28, 0.06, 1.25),
        "Index": (-0.26, -0.05, 1.32),
        "Middle": (-0.24, -0.05, 1.35),
        "Ring": (-0.22, -0.05, 1.32),
        "Pinky": (-0.20, -0.05, 1.28)
    }
}

finger_count = 0
for side in ["left", "right"]:
    for finger_name, base_pos in finger_positions[side].items():
        for segment in range(1, 4):  # 3 segments per finger
            finger_obj_name = f"Finger_{side.title()}_{finger_name}_Seg{segment}"
            finger_obj = bpy.data.objects.get(finger_obj_name)
            if finger_obj:
                # Position each segment slightly forward from the base
                segment_offset = (segment - 1) * 0.025
                finger_obj.location = Vector((
                    base_pos[0],
                    base_pos[1] - segment_offset,
                    base_pos[2]
                ))
                finger_count += 1

print(f"Positioned {finger_count} finger segments")

# 4. Position toes
print("🦶 Positioning toes...")

toe_positions = {
    "left": [(0.1 + (i-2)*0.02, -0.15, 0.07) for i in range(5)],
    "right": [(-0.1 + (i-2)*0.02, -0.15, 0.07) for i in range(5)]
}

toe_count = 0
for side in ["left", "right"]:
    for i, toe_pos in enumerate(toe_positions[side]):
        toe_name = f"Toe_{side.title()}_{i+1}"
        toe_obj = bpy.data.objects.get(toe_name)
        if toe_obj:
            toe_obj.location = Vector(toe_pos)
            toe_count += 1

print(f"Positioned {toe_count} toes")

# 5. Adjust camera for full body view
print("📷 Adjusting camera...")

camera = bpy.data.objects.get("Camera")
if camera:
    camera.location = Vector((3, -3, 1.5))
    camera.rotation_euler = (1.1, 0, 0.785)
    if hasattr(camera.data, 'lens'):
        camera.data.lens = 35  # Wider lens for full body
    print("  ✅ Camera positioned for full body view")
else:
    print("  ❌ Camera not found")

# 6. Adjust lighting
print("💡 Adjusting lighting...")

lights = [obj for obj in bpy.data.objects if obj.type == 'LIGHT']
if lights:
    main_light = lights[0]
    main_light.location = Vector((4, 2, 4))
    if hasattr(main_light.data, 'energy'):
        main_light.data.energy = 8.0
    print(f"  ✅ Adjusted {main_light.name}")
else:
    print("  ❌ No lights found")

# 7. Add a ground plane for reference
print("🌍 Adding ground plane...")

# Remove existing ground if any
if "Ground" in bpy.data.objects:
    bpy.data.objects.remove(bpy.data.objects["Ground"], do_unlink=True)

# Create ground plane
bpy.ops.mesh.primitive_plane_add(size=10, location=(0, 0, -0.1))
ground = bpy.context.object
ground.name = "Ground"

# Create simple ground material
if "Ground_Material" not in bpy.data.materials:
    ground_mat = bpy.data.materials.new(name="Ground_Material")
    ground_mat.use_nodes = True
    
    # Clear existing nodes
    nodes = ground_mat.node_tree.nodes
    nodes.clear()
    
    # Add new nodes
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    
    # Set material properties
    principled.inputs['Base Color'].default_value = (0.8, 0.8, 0.8, 1.0)
    principled.inputs['Roughness'].default_value = 0.9
    
    # Link nodes
    ground_mat.node_tree.links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    # Assign material
    ground.data.materials.append(ground_mat)
    print("  ✅ Ground plane with material added")
else:
    ground.data.materials.append(bpy.data.materials["Ground_Material"])
    print("  ✅ Ground plane added with existing material")

print("\n🎉 BODY ASSEMBLY FIXED!")
print("📋 Summary of fixes:")
print(f"  📍 Positioned {positioned_count} main body parts")
print(f"  🖐️ Positioned {finger_count} finger segments")
print(f"  🦶 Positioned {toe_count} toes")
print("  📷 Camera adjusted for full body view")
print("  💡 Lighting improved")
print("  🌍 Ground plane added")
print("")
print("💡 Now press F12 to render and see the complete human figure!")
print("🔧 You should now see a properly assembled human body with:")
print("  👤 All body parts in correct positions")
print("  ✋ Hands with fingers")
print("  🦶 Feet with toes")
print("  👁️ Facial features positioned correctly")
print("  💇 Hair on the head")
print("  🌍 Standing on a ground plane")
