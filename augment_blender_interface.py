"""
Augment Agent - Blender Interface
Direct communication interface between Augment Agent and Blender
"""

import socket
import json
import time
import tempfile
import os
from typing import Dict, Any, Optional, List

class AugmentBlenderInterface:
    """Direct interface for Augment Agent to control Blender"""
    
    def __init__(self, host='localhost', port=9876):
        self.host = host
        self.port = port
        self.socket = None
        self.connected = False
    
    def connect(self) -> bool:
        """Establish connection to Blender MCP server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)
            self.socket.connect((self.host, self.port))
            self.connected = True
            print(f"✅ Connected to <PERSON>lender at {self.host}:{self.port}")
            return True
        except Exception as e:
            print(f"❌ Failed to connect to Blender: {str(e)}")
            self.connected = False
            return False
    
    def disconnect(self):
        """Close connection to <PERSON>lender"""
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        self.connected = False
        print("🔌 Disconnected from Blender")
    
    def send_command(self, command_type: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Send a command to Blender and return the response"""
        if not self.connected:
            return {"error": "Not connected to Blender. Call connect() first."}
        
        if params is None:
            params = {}
        
        command = {
            "type": command_type,
            "params": params
        }
        
        try:
            # Send command
            command_json = json.dumps(command)
            self.socket.sendall(command_json.encode('utf-8'))
            
            # Receive response
            response_data = self.socket.recv(8192)
            response = json.loads(response_data.decode('utf-8'))
            
            return response
        except Exception as e:
            return {"error": f"Communication error: {str(e)}"}
    
    # Scene Information Methods
    def get_scene_info(self) -> Dict[str, Any]:
        """Get information about the current Blender scene"""
        return self.send_command("get_scene_info")
    
    def get_object_info(self, object_name: str) -> Dict[str, Any]:
        """Get detailed information about a specific object"""
        return self.send_command("get_object_info", {"name": object_name})
    
    def take_screenshot(self, filepath: str = None, max_size: int = 800) -> Dict[str, Any]:
        """Take a screenshot of the current viewport"""
        if filepath is None:
            # Create temporary file
            temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            filepath = temp_file.name
            temp_file.close()
        
        result = self.send_command("get_viewport_screenshot", {
            "filepath": filepath,
            "max_size": max_size,
            "format": "png"
        })
        
        if result.get("status") == "success":
            result["result"]["local_filepath"] = filepath
        
        return result
    
    # Code Execution Methods
    def execute_blender_code(self, code: str) -> Dict[str, Any]:
        """Execute Python code in Blender"""
        return self.send_command("execute_code", {"code": code})
    
    def create_object(self, object_type: str, location: List[float] = None, name: str = None) -> Dict[str, Any]:
        """Create a new object in Blender"""
        if location is None:
            location = [0, 0, 0]
        
        code = f"""
import bpy

# Create {object_type}
bpy.ops.mesh.primitive_{object_type}_add(location=({location[0]}, {location[1]}, {location[2]}))
obj = bpy.context.active_object

if "{name}" and "{name}" != "None":
    obj.name = "{name}"

print(f"Created {{obj.type}} object: {{obj.name}} at {{obj.location}}")
"""
        return self.execute_blender_code(code)
    
    def delete_object(self, object_name: str) -> Dict[str, Any]:
        """Delete an object from Blender"""
        code = f"""
import bpy

obj = bpy.data.objects.get("{object_name}")
if obj:
    bpy.data.objects.remove(obj, do_unlink=True)
    print(f"Deleted object: {object_name}")
else:
    print(f"Object not found: {object_name}")
"""
        return self.execute_blender_code(code)
    
    def move_object(self, object_name: str, location: List[float]) -> Dict[str, Any]:
        """Move an object to a new location"""
        code = f"""
import bpy

obj = bpy.data.objects.get("{object_name}")
if obj:
    obj.location = ({location[0]}, {location[1]}, {location[2]})
    print(f"Moved {{obj.name}} to {{obj.location}}")
else:
    print(f"Object not found: {object_name}")
"""
        return self.execute_blender_code(code)
    
    def create_material(self, material_name: str, color: List[float] = None) -> Dict[str, Any]:
        """Create a new material"""
        if color is None:
            color = [0.8, 0.2, 0.2, 1.0]  # Default red
        
        code = f"""
import bpy

# Create material
mat = bpy.data.materials.new(name="{material_name}")
mat.use_nodes = True

# Set color
principled = mat.node_tree.nodes.get("Principled BSDF")
if principled:
    principled.inputs[0].default_value = ({color[0]}, {color[1]}, {color[2]}, {color[3]})

print(f"Created material: {material_name}")
"""
        return self.execute_blender_code(code)
    
    def apply_material(self, object_name: str, material_name: str) -> Dict[str, Any]:
        """Apply a material to an object"""
        code = f"""
import bpy

obj = bpy.data.objects.get("{object_name}")
mat = bpy.data.materials.get("{material_name}")

if obj and mat:
    # Clear existing materials
    obj.data.materials.clear()
    # Add new material
    obj.data.materials.append(mat)
    print(f"Applied material {{mat.name}} to {{obj.name}}")
else:
    if not obj:
        print(f"Object not found: {object_name}")
    if not mat:
        print(f"Material not found: {material_name}")
"""
        return self.execute_blender_code(code)
    
    # Animation Methods
    def create_keyframe(self, object_name: str, property_name: str, frame: int) -> Dict[str, Any]:
        """Create a keyframe for an object property"""
        code = f"""
import bpy

obj = bpy.data.objects.get("{object_name}")
if obj:
    bpy.context.scene.frame_set({frame})
    obj.keyframe_insert(data_path="{property_name}")
    print(f"Created keyframe for {{obj.name}}.{property_name} at frame {frame}")
else:
    print(f"Object not found: {object_name}")
"""
        return self.execute_blender_code(code)
    
    def set_animation_range(self, start_frame: int, end_frame: int) -> Dict[str, Any]:
        """Set the animation frame range"""
        code = f"""
import bpy

bpy.context.scene.frame_start = {start_frame}
bpy.context.scene.frame_end = {end_frame}
print(f"Set animation range: {start_frame} to {end_frame}")
"""
        return self.execute_blender_code(code)
    
    # Utility Methods
    def clear_scene(self) -> Dict[str, Any]:
        """Clear all objects from the scene"""
        code = """
import bpy

# Select all mesh objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False, confirm=False)

print("Cleared all objects from scene")
"""
        return self.execute_blender_code(code)
    
    def run_animation_example(self, example_name: str) -> Dict[str, Any]:
        """Run one of the predefined animation examples"""
        # Import the examples
        try:
            from example_animations import ANIMATION_EXAMPLES
            if example_name in ANIMATION_EXAMPLES:
                code = ANIMATION_EXAMPLES[example_name]
                return self.execute_blender_code(code)
            else:
                return {"error": f"Animation example '{example_name}' not found. Available: {list(ANIMATION_EXAMPLES.keys())}"}
        except ImportError:
            return {"error": "example_animations.py not found"}
    
    # Context manager support
    def __enter__(self):
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.disconnect()

# Convenience function for quick operations
def quick_blender_command(command_type: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
    """Execute a single command without maintaining connection"""
    with AugmentBlenderInterface() as blender:
        if blender.connected:
            return blender.send_command(command_type, params)
        else:
            return {"error": "Could not connect to Blender"}

# Example usage functions
def demo_basic_operations():
    """Demonstrate basic Blender operations"""
    with AugmentBlenderInterface() as blender:
        if not blender.connected:
            print("❌ Could not connect to Blender")
            return
        
        print("🎬 Running Blender Demo...")
        
        # Get scene info
        scene = blender.get_scene_info()
        print(f"📋 Scene: {scene}")
        
        # Clear scene
        blender.clear_scene()
        
        # Create objects
        blender.create_object("cube", [0, 0, 0], "MyCube")
        blender.create_object("uv_sphere", [3, 0, 0], "MySphere")
        
        # Create and apply materials
        blender.create_material("RedMaterial", [1, 0, 0, 1])
        blender.create_material("BlueMaterial", [0, 0, 1, 1])
        
        blender.apply_material("MyCube", "RedMaterial")
        blender.apply_material("MySphere", "BlueMaterial")
        
        print("✅ Demo complete!")

if __name__ == "__main__":
    demo_basic_operations()
