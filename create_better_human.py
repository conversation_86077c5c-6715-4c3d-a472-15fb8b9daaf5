#!/usr/bin/env python3
"""
Create a much better human body using metaballs and proper modeling
"""

from augment_blender_interface import AugmentBlenderInterface

def create_better_human():
    """Create a natural-looking human body using metaballs"""
    
    better_human_code = '''
import bpy
import mathutils

print("🧑 Creating better human body...")

# Delete the current robotic body
current_body = bpy.data.objects.get("Human_Body")
if current_body:
    bpy.data.objects.remove(current_body, do_unlink=True)
    print("🗑️ Removed robotic body")

# Get the rig
human_rig = bpy.data.objects.get("Human_Rig")
if not human_rig:
    human_rig = bpy.data.objects.get("RIG-Human_Metarig")

# Clear selection
bpy.ops.object.select_all(action='DESELECT')

# Method: Use metaballs for organic shapes, then convert to mesh
print("🔮 Creating organic body with metaballs...")

# Create metaball for head
bpy.ops.object.metaball_add(type='BALL', location=(0, 0, 1.75))
head_meta = bpy.context.active_object
head_meta.name = "Head_Meta"
head_meta.data.elements[0].radius = 0.15
head_meta.data.elements[0].stiffness = 0.75

# Create metaball for neck
bpy.ops.object.metaball_add(type='ELLIPSOID', location=(0, 0, 1.55))
neck_meta = bpy.context.active_object
neck_meta.name = "Neck_Meta"
neck_meta.data.elements[0].radius = 0.08
neck_meta.data.elements[0].size_x = 0.8
neck_meta.data.elements[0].size_y = 0.8
neck_meta.data.elements[0].size_z = 1.5

# Create metaball for torso
bpy.ops.object.metaball_add(type='ELLIPSOID', location=(0, 0, 1.2))
torso_meta = bpy.context.active_object
torso_meta.name = "Torso_Meta"
torso_meta.data.elements[0].radius = 0.25
torso_meta.data.elements[0].size_x = 1.2
torso_meta.data.elements[0].size_y = 0.8
torso_meta.data.elements[0].size_z = 1.8

# Create metaball for pelvis
bpy.ops.object.metaball_add(type='ELLIPSOID', location=(0, 0, 0.85))
pelvis_meta = bpy.context.active_object
pelvis_meta.name = "Pelvis_Meta"
pelvis_meta.data.elements[0].radius = 0.18
pelvis_meta.data.elements[0].size_x = 1.1
pelvis_meta.data.elements[0].size_y = 0.9
pelvis_meta.data.elements[0].size_z = 1.2

# Create metaballs for arms
# Left upper arm
bpy.ops.object.metaball_add(type='ELLIPSOID', location=(0.35, 0, 1.35))
l_upper_arm_meta = bpy.context.active_object
l_upper_arm_meta.name = "L_UpperArm_Meta"
l_upper_arm_meta.data.elements[0].radius = 0.08
l_upper_arm_meta.data.elements[0].size_x = 2.5
l_upper_arm_meta.data.elements[0].size_y = 1.0
l_upper_arm_meta.data.elements[0].size_z = 1.0
l_upper_arm_meta.rotation_euler = (0, 0, 1.57)  # Rotate to horizontal

# Left forearm
bpy.ops.object.metaball_add(type='ELLIPSOID', location=(0.6, 0, 1.1))
l_forearm_meta = bpy.context.active_object
l_forearm_meta.name = "L_Forearm_Meta"
l_forearm_meta.data.elements[0].radius = 0.06
l_forearm_meta.data.elements[0].size_x = 2.0
l_forearm_meta.data.elements[0].size_y = 1.0
l_forearm_meta.data.elements[0].size_z = 1.0
l_forearm_meta.rotation_euler = (0, 0, 1.57)

# Left hand
bpy.ops.object.metaball_add(type='ELLIPSOID', location=(0.8, 0, 1.0))
l_hand_meta = bpy.context.active_object
l_hand_meta.name = "L_Hand_Meta"
l_hand_meta.data.elements[0].radius = 0.05
l_hand_meta.data.elements[0].size_x = 1.5
l_hand_meta.data.elements[0].size_y = 0.6
l_hand_meta.data.elements[0].size_z = 1.2

# Right upper arm
bpy.ops.object.metaball_add(type='ELLIPSOID', location=(-0.35, 0, 1.35))
r_upper_arm_meta = bpy.context.active_object
r_upper_arm_meta.name = "R_UpperArm_Meta"
r_upper_arm_meta.data.elements[0].radius = 0.08
r_upper_arm_meta.data.elements[0].size_x = 2.5
r_upper_arm_meta.data.elements[0].size_y = 1.0
r_upper_arm_meta.data.elements[0].size_z = 1.0
r_upper_arm_meta.rotation_euler = (0, 0, 1.57)

# Right forearm
bpy.ops.object.metaball_add(type='ELLIPSOID', location=(-0.6, 0, 1.1))
r_forearm_meta = bpy.context.active_object
r_forearm_meta.name = "R_Forearm_Meta"
r_forearm_meta.data.elements[0].radius = 0.06
r_forearm_meta.data.elements[0].size_x = 2.0
r_forearm_meta.data.elements[0].size_y = 1.0
r_forearm_meta.data.elements[0].size_z = 1.0
r_forearm_meta.rotation_euler = (0, 0, 1.57)

# Right hand
bpy.ops.object.metaball_add(type='ELLIPSOID', location=(-0.8, 0, 1.0))
r_hand_meta = bpy.context.active_object
r_hand_meta.name = "R_Hand_Meta"
r_hand_meta.data.elements[0].radius = 0.05
r_hand_meta.data.elements[0].size_x = 1.5
r_hand_meta.data.elements[0].size_y = 0.6
r_hand_meta.data.elements[0].size_z = 1.2

# Create metaballs for legs
# Left thigh
bpy.ops.object.metaball_add(type='ELLIPSOID', location=(0.12, 0, 0.5))
l_thigh_meta = bpy.context.active_object
l_thigh_meta.name = "L_Thigh_Meta"
l_thigh_meta.data.elements[0].radius = 0.1
l_thigh_meta.data.elements[0].size_x = 1.0
l_thigh_meta.data.elements[0].size_y = 1.0
l_thigh_meta.data.elements[0].size_z = 2.2

# Left shin
bpy.ops.object.metaball_add(type='ELLIPSOID', location=(0.12, 0, 0.1))
l_shin_meta = bpy.context.active_object
l_shin_meta.name = "L_Shin_Meta"
l_shin_meta.data.elements[0].radius = 0.08
l_shin_meta.data.elements[0].size_x = 1.0
l_shin_meta.data.elements[0].size_y = 1.0
l_shin_meta.data.elements[0].size_z = 2.0

# Left foot
bpy.ops.object.metaball_add(type='ELLIPSOID', location=(0.12, 0.08, -0.15))
l_foot_meta = bpy.context.active_object
l_foot_meta.name = "L_Foot_Meta"
l_foot_meta.data.elements[0].radius = 0.06
l_foot_meta.data.elements[0].size_x = 0.8
l_foot_meta.data.elements[0].size_y = 2.0
l_foot_meta.data.elements[0].size_z = 0.8

# Right thigh
bpy.ops.object.metaball_add(type='ELLIPSOID', location=(-0.12, 0, 0.5))
r_thigh_meta = bpy.context.active_object
r_thigh_meta.name = "R_Thigh_Meta"
r_thigh_meta.data.elements[0].radius = 0.1
r_thigh_meta.data.elements[0].size_x = 1.0
r_thigh_meta.data.elements[0].size_y = 1.0
r_thigh_meta.data.elements[0].size_z = 2.2

# Right shin
bpy.ops.object.metaball_add(type='ELLIPSOID', location=(-0.12, 0, 0.1))
r_shin_meta = bpy.context.active_object
r_shin_meta.name = "R_Shin_Meta"
r_shin_meta.data.elements[0].radius = 0.08
r_shin_meta.data.elements[0].size_x = 1.0
r_shin_meta.data.elements[0].size_y = 1.0
r_shin_meta.data.elements[0].size_z = 2.0

# Right foot
bpy.ops.object.metaball_add(type='ELLIPSOID', location=(-0.12, 0.08, -0.15))
r_foot_meta = bpy.context.active_object
r_foot_meta.name = "R_Foot_Meta"
r_foot_meta.data.elements[0].radius = 0.06
r_foot_meta.data.elements[0].size_x = 0.8
r_foot_meta.data.elements[0].size_y = 2.0
r_foot_meta.data.elements[0].size_z = 0.8

print("✅ Created all metaball body parts")

# Now convert the first metaball to mesh (this will include all connected metaballs)
bpy.ops.object.select_all(action='DESELECT')
head_meta.select_set(True)
bpy.context.view_layer.objects.active = head_meta
bpy.ops.object.convert(target='MESH')

# Rename to Human_Body
human_body = bpy.context.active_object
human_body.name = "Human_Body"

print("✅ Converted metaballs to organic mesh")

# Apply smooth shading
bpy.ops.object.shade_smooth()

# Add subdivision surface for even smoother look
human_body.modifiers.new(name="Subdivision", type='SUBSURF')
human_body.modifiers["Subdivision"].levels = 1
human_body.modifiers["Subdivision"].render_levels = 2

print("✅ Applied smooth shading and subdivision")

# Create natural skin material
mat = bpy.data.materials.new(name="Natural_Skin")
mat.use_nodes = True
nodes = mat.node_tree.nodes
links = mat.node_tree.links

# Clear default nodes
for node in nodes:
    nodes.remove(node)

# Create skin shader
output = nodes.new(type='ShaderNodeOutputMaterial')
output.location = (400, 0)

principled = nodes.new(type='ShaderNodeBsdfPrincipled')
principled.location = (200, 0)

# Natural skin color
principled.inputs[0].default_value = (0.8, 0.6, 0.45, 1.0)  # Base Color
principled.inputs[7].default_value = 0.4  # Roughness
principled.inputs[12].default_value = 1.4  # IOR
principled.inputs[15].default_value = 0.05  # Subsurface

# Connect
links.new(principled.outputs['BSDF'], output.inputs['Surface'])

# Apply material
human_body.data.materials.clear()
human_body.data.materials.append(mat)

print("✅ Applied natural skin material")

# Parent to rig if available
if human_rig:
    bpy.ops.object.select_all(action='DESELECT')
    human_body.select_set(True)
    human_rig.select_set(True)
    bpy.context.view_layer.objects.active = human_rig
    
    try:
        bpy.ops.object.parent_set(type='ARMATURE_AUTO')
        print("✅ Body parented to rig with automatic weights")
    except Exception as e:
        print(f"⚠️ Auto weights failed: {e}")
        bpy.ops.object.parent_set(type='ARMATURE')
        print("✅ Body parented to rig")

print("🎉 Natural human body created!")
print("👤 Much more organic and realistic!")
'''
    
    with AugmentBlenderInterface() as blender:
        if blender.connected:
            print("🧑 Creating better human body...")
            result = blender.execute_blender_code(better_human_code)
            
            if result.get('status') == 'success':
                output = result.get('result', {}).get('result', '')
                print("Creation output:")
                print(output)
                
                print("\n🎉 Better human body created!")
                print("👤 The character should now look much more natural and organic!")
                print("🎬 Ready for realistic character animation!")
            else:
                print(f"❌ Error: {result.get('message', 'Unknown error')}")
        else:
            print("❌ Could not connect to Blender")

if __name__ == "__main__":
    create_better_human()
