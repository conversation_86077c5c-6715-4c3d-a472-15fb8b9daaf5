#!/usr/bin/env python3
"""
Run scene check from external Python
"""

from augment_blender_interface import AugmentBlenderInterface

def run_scene_check():
    """Check what's in the Blender scene"""
    
    # Simple code without f-strings to avoid JSON issues
    check_code = '''
import bpy

print("=== BLENDER SCENE CHECK ===")

print("All objects:")
for obj in bpy.data.objects:
    print(obj.name, obj.type, "at", obj.location)

print("Mesh objects:")
mesh_count = 0
for obj in bpy.data.objects:
    if obj.type == 'MESH':
        mesh_count += 1
        print("MESH:", obj.name, "at", obj.location)

print("Total meshes:", mesh_count)

print("Camera:")
camera = bpy.data.objects.get("Camera")
if camera:
    print("Camera found at", camera.location)
else:
    print("No camera")

print("=== CHECK COMPLETE ===")
'''
    
    try:
        with AugmentBlenderInterface() as blender:
            if blender.connected:
                print("🔍 Checking Blender scene...")
                result = blender.execute_blender_code(check_code)
                
                if result.get('status') == 'success':
                    output = result.get('result', {}).get('result', '')
                    print("✅ Scene check successful!")
                    print("Output:")
                    print(output)
                else:
                    print(f"❌ Error: {result}")
            else:
                print("❌ Could not connect to Blender")
                print("Make sure Blender is running with the MCP server")
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    run_scene_check()
