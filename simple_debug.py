#!/usr/bin/env python3
"""
Very simple debug
"""

from augment_blender_interface import AugmentBlenderInterface

def simple_debug():
    """Simple debug"""
    
    debug_code = '''
import bpy
print("Objects in scene:")
for obj in bpy.data.objects:
    if obj.type == 'MESH':
        print(obj.name, obj.location)
'''
    
    with AugmentBlenderInterface() as blender:
        if blender.connected:
            print("🔍 Simple debug...")
            result = blender.execute_blender_code(debug_code)
            
            if result.get('status') == 'success':
                output = result.get('result', {}).get('result', '')
                print("Debug output:")
                print(output)
            else:
                print(f"❌ Error: {result}")
        else:
            print("❌ Could not connect to Blender")

if __name__ == "__main__":
    simple_debug()
