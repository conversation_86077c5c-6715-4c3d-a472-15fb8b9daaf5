#!/usr/bin/env python3
"""
Switch to proper view mode to see the human body
"""

from augment_blender_interface import AugmentBlenderInterface

def show_human_body():
    """Switch view modes to show the human body properly"""
    
    show_body_code = '''
import bpy

print("👁️ Switching to proper view mode to show human body...")

# Get the human body
human_body = bpy.data.objects.get("Human_Body")
human_rig = bpy.data.objects.get("Human_Rig")
if not human_rig:
    human_rig = bpy.data.objects.get("RIG-Human_Metarig")

if human_body:
    print(f"✅ Found human body: {human_body.name}")
    
    # Make sure the body is visible
    human_body.hide_viewport = False
    human_body.hide_render = False
    
    # Switch to solid shading mode
    for area in bpy.context.screen.areas:
        if area.type == 'VIEW_3D':
            for space in area.spaces:
                if space.type == 'VIEW_3D':
                    space.shading.type = 'SOLID'  # Switch from wireframe to solid
                    space.shading.color_type = 'MATERIAL'  # Show materials
                    space.overlay.show_wireframes = False  # Hide wireframes
                    print("✅ Switched to solid shading mode")
    
    # Set the rig to show bones in front but not dominate
    if human_rig:
        human_rig.data.display_type = 'STICK'  # Less intrusive bone display
        human_rig.show_in_front = False  # Don't show bones in front of mesh
        print("✅ Set rig to less intrusive display")
    
    # Select and focus on the human body
    bpy.ops.object.select_all(action='DESELECT')
    human_body.select_set(True)
    bpy.context.view_layer.objects.active = human_body
    
    # Frame the object in view (only if in 3D view context)
    try:
        bpy.ops.view3d.view_selected()
    except:
        print("⚠️ Could not frame view (not in 3D viewport context)")
    
    print("✅ Human body should now be visible!")
    print("👤 You should see the organic human mesh!")
    
else:
    print("❌ Human body not found!")
    
    # List all objects to see what's available
    print("📋 Available objects:")
    for obj in bpy.data.objects:
        print(f"  - {obj.name} ({obj.type})")
'''
    
    with AugmentBlenderInterface() as blender:
        if blender.connected:
            print("👁️ Switching view mode to show human body...")
            result = blender.execute_blender_code(show_body_code)
            
            if result.get('status') == 'success':
                output = result.get('result', {}).get('result', '')
                print("View switch output:")
                print(output)
                
                print("\n👁️ View mode switched!")
                print("👤 You should now see the human body mesh instead of just the rig!")
                print("💡 If you still only see wireframes, try pressing 'Z' and selecting 'Solid' or 'Material Preview'")
            else:
                print(f"❌ Error: {result.get('message', 'Unknown error')}")
        else:
            print("❌ Could not connect to Blender")

if __name__ == "__main__":
    show_human_body()
