#!/usr/bin/env python3
"""
Create a realistic human-shaped body for the existing rig
"""

from augment_blender_interface import AugmentBlenderInterface

def create_realistic_human_body():
    """Replace the cube with a proper human-shaped body"""
    
    realistic_body_code = '''
import bpy

print("🧑 Creating realistic human body shape...")

# Find and delete the current cube body
cube_body = bpy.data.objects.get("Human_Body")
if cube_body:
    bpy.data.objects.remove(cube_body, do_unlink=True)
    print("🗑️ Removed cube body")

# Get the rig for later parenting
human_rig = bpy.data.objects.get("Human_Rig")
if not human_rig:
    human_rig = bpy.data.objects.get("RIG-Human_Metarig")

# Clear selection
bpy.ops.object.select_all(action='DESELECT')

# Create body parts using Blender primitives

# 1. HEAD
bpy.ops.mesh.primitive_uv_sphere_add(radius=0.12, location=(0, 0, 1.9))
head = bpy.context.active_object
head.name = "Head_Part"

# 2. NECK
bpy.ops.mesh.primitive_cylinder_add(radius=0.06, depth=0.15, location=(0, 0, 1.75))
neck = bpy.context.active_object
neck.name = "Neck_Part"

# 3. TORSO (chest)
bpy.ops.mesh.primitive_cube_add(size=1, location=(0, 0, 1.4))
torso = bpy.context.active_object
torso.name = "Torso_Part"
torso.scale = (0.35, 0.2, 0.5)
bpy.ops.object.transform_apply(scale=True)

# 4. ABDOMEN
bpy.ops.mesh.primitive_cube_add(size=1, location=(0, 0, 1.0))
abdomen = bpy.context.active_object
abdomen.name = "Abdomen_Part"
abdomen.scale = (0.32, 0.18, 0.3)
bpy.ops.object.transform_apply(scale=True)

# 5. PELVIS
bpy.ops.mesh.primitive_cube_add(size=1, location=(0, 0, 0.7))
pelvis = bpy.context.active_object
pelvis.name = "Pelvis_Part"
pelvis.scale = (0.4, 0.25, 0.2)
bpy.ops.object.transform_apply(scale=True)

# 6. LEFT ARM
bpy.ops.mesh.primitive_cylinder_add(radius=0.08, depth=0.6, location=(0.5, 0, 1.5))
l_arm = bpy.context.active_object
l_arm.name = "L_Arm_Part"

# 7. LEFT FOREARM
bpy.ops.mesh.primitive_cylinder_add(radius=0.07, depth=0.5, location=(0.8, 0, 1.2))
l_forearm = bpy.context.active_object
l_forearm.name = "L_Forearm_Part"

# 8. LEFT HAND
bpy.ops.mesh.primitive_cube_add(size=0.15, location=(1.1, 0, 1.1))
l_hand = bpy.context.active_object
l_hand.name = "L_Hand_Part"
l_hand.scale = (1, 0.5, 1.5)
bpy.ops.object.transform_apply(scale=True)

# 9. RIGHT ARM
bpy.ops.mesh.primitive_cylinder_add(radius=0.08, depth=0.6, location=(-0.5, 0, 1.5))
r_arm = bpy.context.active_object
r_arm.name = "R_Arm_Part"

# 10. RIGHT FOREARM
bpy.ops.mesh.primitive_cylinder_add(radius=0.07, depth=0.5, location=(-0.8, 0, 1.2))
r_forearm = bpy.context.active_object
r_forearm.name = "R_Forearm_Part"

# 11. RIGHT HAND
bpy.ops.mesh.primitive_cube_add(size=0.15, location=(-1.1, 0, 1.1))
r_hand = bpy.context.active_object
r_hand.name = "R_Hand_Part"
r_hand.scale = (1, 0.5, 1.5)
bpy.ops.object.transform_apply(scale=True)

# 12. LEFT THIGH
bpy.ops.mesh.primitive_cylinder_add(radius=0.12, depth=0.5, location=(0.15, 0, 0.25))
l_thigh = bpy.context.active_object
l_thigh.name = "L_Thigh_Part"

# 13. LEFT SHIN
bpy.ops.mesh.primitive_cylinder_add(radius=0.09, depth=0.5, location=(0.15, 0, -0.25))
l_shin = bpy.context.active_object
l_shin.name = "L_Shin_Part"

# 14. LEFT FOOT
bpy.ops.mesh.primitive_cube_add(size=0.3, location=(0.15, 0.1, -0.55))
l_foot = bpy.context.active_object
l_foot.name = "L_Foot_Part"
l_foot.scale = (0.4, 1.5, 0.3)
bpy.ops.object.transform_apply(scale=True)

# 15. RIGHT THIGH
bpy.ops.mesh.primitive_cylinder_add(radius=0.12, depth=0.5, location=(-0.15, 0, 0.25))
r_thigh = bpy.context.active_object
r_thigh.name = "R_Thigh_Part"

# 16. RIGHT SHIN
bpy.ops.mesh.primitive_cylinder_add(radius=0.09, depth=0.5, location=(-0.15, 0, -0.25))
r_shin = bpy.context.active_object
r_shin.name = "R_Shin_Part"

# 17. RIGHT FOOT
bpy.ops.mesh.primitive_cube_add(size=0.3, location=(-0.15, 0.1, -0.55))
r_foot = bpy.context.active_object
r_foot.name = "R_Foot_Part"
r_foot.scale = (0.4, 1.5, 0.3)
bpy.ops.object.transform_apply(scale=True)

print("✅ Created all body parts")

# Join all parts into one mesh
body_parts = [head, neck, torso, abdomen, pelvis, l_arm, l_forearm, l_hand,
              r_arm, r_forearm, r_hand, l_thigh, l_shin, l_foot, r_thigh, r_shin, r_foot]

# Select all body parts
bpy.ops.object.select_all(action='DESELECT')
for part in body_parts:
    part.select_set(True)

# Set the torso as active object
bpy.context.view_layer.objects.active = torso

# Join all parts
bpy.ops.object.join()
joined_body = bpy.context.active_object
joined_body.name = "Human_Body"

# Set smooth shading
bpy.ops.object.shade_smooth()

print("✅ Created realistic human body shape")

# Create better skin material
mat = bpy.data.materials.new(name="Realistic_Skin")
mat.use_nodes = True
nodes = mat.node_tree.nodes
links = mat.node_tree.links

# Clear default nodes
for node in nodes:
    nodes.remove(node)

# Create nodes for realistic skin
output = nodes.new(type='ShaderNodeOutputMaterial')
output.location = (400, 0)

principled = nodes.new(type='ShaderNodeBsdfPrincipled')
principled.location = (0, 0)

# Set realistic skin values
principled.inputs[0].default_value = (0.8, 0.6, 0.5, 1.0)  # Base Color - skin tone
principled.inputs[7].default_value = 0.4  # Roughness
try:
    principled.inputs[1].default_value = 0.1  # Subsurface if available
    principled.inputs[3].default_value = (0.9, 0.7, 0.6, 1.0)  # Subsurface Color
except:
    pass  # Skip if subsurface not available in this Blender version

# Connect nodes
links.new(principled.outputs['BSDF'], output.inputs['Surface'])

# Apply material
joined_body.data.materials.append(mat)

print("✅ Applied realistic skin material")

# Parent to rig with automatic weights
if human_rig:
    bpy.ops.object.select_all(action='DESELECT')
    joined_body.select_set(True)
    human_rig.select_set(True)
    bpy.context.view_layer.objects.active = human_rig

    try:
        bpy.ops.object.parent_set(type='ARMATURE_AUTO')
        print("✅ Realistic body parented to rig with automatic weights")
    except Exception as e:
        print(f"⚠️ Auto weights failed: {e}")
        bpy.ops.object.parent_set(type='ARMATURE')
        print("✅ Body parented to rig (manual weights needed)")

# Add subdivision surface for smoother appearance
joined_body.modifiers.new(name="Subdivision", type='SUBSURF')
joined_body.modifiers["Subdivision"].levels = 1
print("✅ Added subdivision surface for smoother appearance")

print("🎉 Realistic human body created!")
print("👤 The character now has:")
print("   - Proper human proportions")
print("   - Head, torso, arms, hands")
print("   - Pelvis, legs, feet")
print("   - Realistic skin material")
print("   - Smooth subdivision surface")
'''
    
    with AugmentBlenderInterface() as blender:
        if blender.connected:
            print("🧑 Creating realistic human body shape...")
            result = blender.execute_blender_code(realistic_body_code)
            
            if result.get('status') == 'success':
                output = result.get('result', {}).get('result', '')
                print("Creation output:")
                print(output)
                
                # Get scene info
                scene = blender.get_scene_info()
                if scene.get('status') == 'success':
                    objects = scene['result']['objects']
                    print(f"\n📋 Scene now contains {len(objects)} objects:")
                    for obj in objects:
                        print(f"   - {obj['name']} ({obj['type']})")
                
                print("\n🎉 Realistic human body created!")
                print("👤 Your character now looks like an actual human!")
                print("🎬 Ready for realistic character animation!")
            else:
                print(f"❌ Error: {result.get('message', 'Unknown error')}")
        else:
            print("❌ Could not connect to Blender")

if __name__ == "__main__":
    create_realistic_human_body()
