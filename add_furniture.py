#!/usr/bin/env python3
"""
Add table and chair inside the house
"""

from augment_blender_interface import AugmentBlenderInterface

def add_furniture():
    """Add table and chair inside the house"""
    
    furniture_code = '''
import bpy
import bmesh
from mathutils import Vector

print("🪑 Adding furniture to the house...")

# Clear any existing furniture
furniture_names = ["Table", "Chair", "Table_Leg_1", "Table_Leg_2", "Table_Leg_3", "Table_Leg_4", 
                  "Chair_Seat", "Chair_Back", "Chair_Leg_1", "Chair_Leg_2", "Chair_Leg_3", "Chair_Leg_4"]

for name in furniture_names:
    if name in bpy.data.objects:
        bpy.data.objects.remove(bpy.data.objects[name], do_unlink=True)

print("🗑️ Cleared existing furniture")

# 1. CREATE TABLE
print("🪑 Creating table...")

# Table top
bpy.ops.mesh.primitive_cube_add(size=1, location=(0, 0, 0.8))
table_top = bpy.context.object
table_top.name = "Table_Top"
table_top.scale = (1.2, 0.8, 0.05)  # Wide, deep, thin table top

# Table legs
leg_positions = [
    (0.9, 0.6, 0.4),   # Front right
    (-0.9, 0.6, 0.4),  # Front left  
    (0.9, -0.6, 0.4),  # Back right
    (-0.9, -0.6, 0.4)  # Back left
]

for i, pos in enumerate(leg_positions):
    bpy.ops.mesh.primitive_cube_add(size=1, location=pos)
    leg = bpy.context.object
    leg.name = f"Table_Leg_{i+1}"
    leg.scale = (0.05, 0.05, 0.4)  # Thin legs

print("✅ Table created with 4 legs")

# 2. CREATE CHAIR
print("🪑 Creating chair...")

# Chair seat
bpy.ops.mesh.primitive_cube_add(size=1, location=(-0.5, 1.2, 0.45))
chair_seat = bpy.context.object
chair_seat.name = "Chair_Seat"
chair_seat.scale = (0.4, 0.4, 0.05)  # Square seat

# Chair back
bpy.ops.mesh.primitive_cube_add(size=1, location=(-0.5, 0.95, 0.7))
chair_back = bpy.context.object
chair_back.name = "Chair_Back"
chair_back.scale = (0.4, 0.05, 0.3)  # Tall back

# Chair legs
chair_leg_positions = [
    (-0.65, 1.35, 0.225),  # Front right
    (-0.35, 1.35, 0.225),  # Front left
    (-0.65, 1.05, 0.225),  # Back right
    (-0.35, 1.05, 0.225)   # Back left
]

for i, pos in enumerate(chair_leg_positions):
    bpy.ops.mesh.primitive_cube_add(size=1, location=pos)
    leg = bpy.context.object
    leg.name = f"Chair_Leg_{i+1}"
    leg.scale = (0.03, 0.03, 0.225)  # Thin chair legs

print("✅ Chair created with seat, back, and 4 legs")

# 3. CREATE MATERIALS
print("🎨 Creating materials...")

# Wood material for table
if "Wood_Material" not in bpy.data.materials:
    wood_mat = bpy.data.materials.new(name="Wood_Material")
    wood_mat.use_nodes = True
    nodes = wood_mat.node_tree.nodes
    nodes.clear()
    
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    
    # Brown wood color
    principled.inputs['Base Color'].default_value = (0.6, 0.4, 0.2, 1.0)
    principled.inputs['Roughness'].default_value = 0.8
    
    wood_mat.node_tree.links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    print("  ✅ Wood material created")

# Apply wood material to table
table_objects = ["Table_Top", "Table_Leg_1", "Table_Leg_2", "Table_Leg_3", "Table_Leg_4"]
for obj_name in table_objects:
    obj = bpy.data.objects.get(obj_name)
    if obj:
        obj.data.materials.clear()
        obj.data.materials.append(bpy.data.materials["Wood_Material"])

# Chair material (darker wood)
if "Chair_Material" not in bpy.data.materials:
    chair_mat = bpy.data.materials.new(name="Chair_Material")
    chair_mat.use_nodes = True
    nodes = chair_mat.node_tree.nodes
    nodes.clear()
    
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    
    # Darker brown for chair
    principled.inputs['Base Color'].default_value = (0.4, 0.25, 0.15, 1.0)
    principled.inputs['Roughness'].default_value = 0.7
    
    chair_mat.node_tree.links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    print("  ✅ Chair material created")

# Apply chair material
chair_objects = ["Chair_Seat", "Chair_Back", "Chair_Leg_1", "Chair_Leg_2", "Chair_Leg_3", "Chair_Leg_4"]
for obj_name in chair_objects:
    obj = bpy.data.objects.get(obj_name)
    if obj:
        obj.data.materials.clear()
        obj.data.materials.append(bpy.data.materials["Chair_Material"])

print("🎨 Materials applied to furniture")

# 4. ADJUST CAMERA FOR INTERIOR VIEW
print("📷 Adjusting camera for interior view...")

camera = bpy.data.objects.get("Camera")
if camera:
    # Position camera to look into the house
    camera.location = Vector((3, -2, 2))
    camera.rotation_euler = (1.0, 0, 0.5)
    print("  ✅ Camera positioned for interior view")

# 5. ADD INTERIOR LIGHTING
print("💡 Adding interior lighting...")

# Add interior light
bpy.ops.object.light_add(type='AREA', location=(0, 0, 2.2))
interior_light = bpy.context.object
interior_light.name = "Interior_Light"
interior_light.data.energy = 15.0
interior_light.data.size = 1.0
interior_light.rotation_euler = (0, 0, 0)

print("  ✅ Interior area light added")

print("\\n🎉 FURNITURE ADDED SUCCESSFULLY!")
print("📋 Added to the house:")
print("  🪑 Wooden table with 4 legs")
print("  🪑 Wooden chair with seat, back, and 4 legs")
print("  🎨 Wood materials for realistic look")
print("  📷 Camera adjusted for interior view")
print("  💡 Interior lighting added")
print("\\n💡 Press F12 to render the furnished house interior!")
'''
    
    with AugmentBlenderInterface() as blender:
        if blender.connected:
            print("🪑 Adding furniture to the house...")
            result = blender.execute_blender_code(furniture_code)
            
            if result.get('status') == 'success':
                output = result.get('result', {}).get('result', '')
                print("Furniture creation output:")
                print(output)
                print("\n✅ Furniture added successfully!")
                print("\n📷 Press F12 to render the furnished house!")
            else:
                print(f"❌ Error adding furniture: {result}")
        else:
            print("❌ Could not connect to Blender")

if __name__ == "__main__":
    add_furniture()
