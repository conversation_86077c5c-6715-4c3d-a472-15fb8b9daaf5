"""
Example Animation Scripts for Blender MCP
These scripts demonstrate common 3D animation tasks that can be executed via AI commands
"""

# Example 1: Create a bouncing ball animation
BOUNCING_BALL = """
import bpy
import mathutils

# Clear existing mesh objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False, confirm=False)

# Create a sphere (ball)
bpy.ops.mesh.primitive_uv_sphere_add(location=(0, 0, 5))
ball = bpy.context.active_object
ball.name = "BouncingBall"

# Add material
mat = bpy.data.materials.new(name="BallMaterial")
mat.use_nodes = True
mat.node_tree.nodes["Principled BSDF"].inputs[0].default_value = (1, 0.2, 0.2, 1)  # Red color
ball.data.materials.append(mat)

# Create ground plane
bpy.ops.mesh.primitive_plane_add(size=10, location=(0, 0, 0))
ground = bpy.context.active_object
ground.name = "Ground"

# Add ground material
ground_mat = bpy.data.materials.new(name="GroundMaterial")
ground_mat.use_nodes = True
ground_mat.node_tree.nodes["Principled BSDF"].inputs[0].default_value = (0.2, 0.8, 0.2, 1)  # Green
ground.data.materials.append(ground_mat)

# Select the ball for animation
bpy.context.view_layer.objects.active = ball
ball.select_set(True)

# Clear existing animation data
ball.animation_data_clear()

# Set up animation keyframes
frame_start = 1
frame_end = 120
bpy.context.scene.frame_start = frame_start
bpy.context.scene.frame_end = frame_end

# Keyframe 1: Ball at top
bpy.context.scene.frame_set(1)
ball.location = (0, 0, 5)
ball.keyframe_insert(data_path="location", index=2)

# Keyframe 2: Ball at bottom (bounce)
bpy.context.scene.frame_set(30)
ball.location = (0, 0, 0.5)
ball.keyframe_insert(data_path="location", index=2)

# Keyframe 3: Ball back up
bpy.context.scene.frame_set(60)
ball.location = (0, 0, 4)
ball.keyframe_insert(data_path="location", index=2)

# Keyframe 4: Ball down again
bpy.context.scene.frame_set(90)
ball.location = (0, 0, 0.5)
ball.keyframe_insert(data_path="location", index=2)

# Keyframe 5: Ball up again
bpy.context.scene.frame_set(120)
ball.location = (0, 0, 3)
ball.keyframe_insert(data_path="location", index=2)

# Set interpolation to ease in/out for realistic bounce
if ball.animation_data and ball.animation_data.action:
    for fcurve in ball.animation_data.action.fcurves:
        for keyframe in fcurve.keyframe_points:
            keyframe.interpolation = 'BEZIER'
            keyframe.handle_left_type = 'AUTO'
            keyframe.handle_right_type = 'AUTO'

print("Bouncing ball animation created! Press spacebar to play.")
"""

# Example 2: Rotating cube with color change
ROTATING_CUBE = """
import bpy
import mathutils

# Clear existing mesh objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False, confirm=False)

# Create a cube
bpy.ops.mesh.primitive_cube_add(location=(0, 0, 0))
cube = bpy.context.active_object
cube.name = "RotatingCube"

# Add material with nodes
mat = bpy.data.materials.new(name="CubeMaterial")
mat.use_nodes = True
nodes = mat.node_tree.nodes
links = mat.node_tree.links

# Clear default nodes
for node in nodes:
    nodes.remove(node)

# Create new nodes
output = nodes.new(type='ShaderNodeOutputMaterial')
principled = nodes.new(type='ShaderNodeBsdfPrincipled')
color_ramp = nodes.new(type='ShaderNodeValToRGB')

# Position nodes
output.location = (300, 0)
principled.location = (0, 0)
color_ramp.location = (-300, 0)

# Set up color ramp
color_ramp.color_ramp.elements[0].color = (1, 0, 0, 1)  # Red
color_ramp.color_ramp.elements[1].color = (0, 0, 1, 1)  # Blue

# Connect nodes
links.new(color_ramp.outputs['Color'], principled.inputs['Base Color'])
links.new(principled.outputs['BSDF'], output.inputs['Surface'])

# Assign material
cube.data.materials.append(mat)

# Set up animation
frame_start = 1
frame_end = 120
bpy.context.scene.frame_start = frame_start
bpy.context.scene.frame_end = frame_end

# Clear existing animation
cube.animation_data_clear()

# Animate rotation
bpy.context.scene.frame_set(1)
cube.rotation_euler = (0, 0, 0)
cube.keyframe_insert(data_path="rotation_euler")

bpy.context.scene.frame_set(120)
cube.rotation_euler = (0, 0, 6.28)  # Full rotation (2π radians)
cube.keyframe_insert(data_path="rotation_euler")

# Animate color (through color ramp factor)
bpy.context.scene.frame_set(1)
color_ramp.inputs[0].default_value = 0.0
color_ramp.inputs[0].keyframe_insert("default_value")

bpy.context.scene.frame_set(60)
color_ramp.inputs[0].default_value = 1.0
color_ramp.inputs[0].keyframe_insert("default_value")

bpy.context.scene.frame_set(120)
color_ramp.inputs[0].default_value = 0.0
color_ramp.inputs[0].keyframe_insert("default_value")

# Set linear interpolation for smooth animation
if cube.animation_data and cube.animation_data.action:
    for fcurve in cube.animation_data.action.fcurves:
        for keyframe in fcurve.keyframe_points:
            keyframe.interpolation = 'LINEAR'

print("Rotating cube with color change created! Press spacebar to play.")
"""

# Example 3: Camera orbit animation
CAMERA_ORBIT = """
import bpy
import mathutils
import math

# Create target object (what camera will look at)
bpy.ops.mesh.primitive_monkey_add(location=(0, 0, 0))
target = bpy.context.active_object
target.name = "CameraTarget"

# Add material to monkey
mat = bpy.data.materials.new(name="MonkeyMaterial")
mat.use_nodes = True
mat.node_tree.nodes["Principled BSDF"].inputs[0].default_value = (0.8, 0.4, 0.1, 1)  # Orange
target.data.materials.append(mat)

# Get or create camera
if "Camera" in bpy.data.objects:
    camera = bpy.data.objects["Camera"]
else:
    bpy.ops.object.camera_add()
    camera = bpy.context.active_object

camera.name = "OrbitCamera"

# Set up animation
frame_start = 1
frame_end = 120
bpy.context.scene.frame_start = frame_start
bpy.context.scene.frame_end = frame_end

# Clear existing animation
camera.animation_data_clear()

# Create orbit animation
radius = 7
height = 3

for frame in range(frame_start, frame_end + 1):
    bpy.context.scene.frame_set(frame)
    
    # Calculate angle based on frame
    angle = (frame - frame_start) / (frame_end - frame_start) * 2 * math.pi
    
    # Calculate camera position
    x = radius * math.cos(angle)
    y = radius * math.sin(angle)
    z = height
    
    camera.location = (x, y, z)
    
    # Point camera at target
    direction = mathutils.Vector((0, 0, 0)) - camera.location
    camera.rotation_euler = direction.to_track_quat('-Z', 'Y').to_euler()
    
    # Insert keyframes
    camera.keyframe_insert(data_path="location")
    camera.keyframe_insert(data_path="rotation_euler")

# Set camera as active
bpy.context.scene.camera = camera

# Set interpolation to linear for smooth orbit
if camera.animation_data and camera.animation_data.action:
    for fcurve in camera.animation_data.action.fcurves:
        for keyframe in fcurve.keyframe_points:
            keyframe.interpolation = 'LINEAR'

print("Camera orbit animation created! Press spacebar to play and switch to camera view (Numpad 0).")
"""

# Example 4: Simple particle system
PARTICLE_FOUNTAIN = """
import bpy

# Clear existing mesh objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False, confirm=False)

# Create emitter object
bpy.ops.mesh.primitive_plane_add(size=0.5, location=(0, 0, 0))
emitter = bpy.context.active_object
emitter.name = "ParticleEmitter"

# Add particle system
bpy.context.view_layer.objects.active = emitter
bpy.ops.object.particle_system_add()

# Get particle system
particle_system = emitter.particle_systems[0]
settings = particle_system.settings

# Configure particle settings
settings.count = 1000
settings.frame_start = 1
settings.frame_end = 200
settings.lifetime = 100
settings.emit_from = 'FACE'
settings.physics_type = 'NEWTON'

# Set initial velocity
settings.normal_factor = 5.0
settings.factor_random = 2.0

# Add gravity
settings.effector_weights.gravity = 1.0

# Set particle appearance
settings.particle_size = 0.05
settings.size_random = 0.5

# Create material for particles
mat = bpy.data.materials.new(name="ParticleMaterial")
mat.use_nodes = True
mat.node_tree.nodes["Principled BSDF"].inputs[0].default_value = (0.1, 0.5, 1.0, 1)  # Blue
mat.node_tree.nodes["Principled BSDF"].inputs[21].default_value = 0.8  # Alpha

# Enable transparency
mat.blend_method = 'BLEND'

emitter.data.materials.append(mat)

# Set animation length
bpy.context.scene.frame_start = 1
bpy.context.scene.frame_end = 200

print("Particle fountain created! Press spacebar to play the animation.")
"""

# Dictionary of all examples for easy access
ANIMATION_EXAMPLES = {
    "bouncing_ball": BOUNCING_BALL,
    "rotating_cube": ROTATING_CUBE,
    "camera_orbit": CAMERA_ORBIT,
    "particle_fountain": PARTICLE_FOUNTAIN
}

def get_animation_script(name):
    """Get an animation script by name"""
    return ANIMATION_EXAMPLES.get(name, "Animation not found!")

def list_animations():
    """List all available animations"""
    return list(ANIMATION_EXAMPLES.keys())

if __name__ == "__main__":
    print("Available animation examples:")
    for name in list_animations():
        print(f"  - {name}")
