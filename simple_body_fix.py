#!/usr/bin/env python3
"""
Simple fix for human body positioning
"""

from augment_blender_interface import AugmentBlenderInterface

def simple_body_fix():
    """Simple fix to position body parts correctly"""
    
    fix_code = '''
import bpy
from mathutils import Vector

print("🔧 Simple body fix starting...")

# List current objects
print("Current objects:")
for obj in bpy.data.objects:
    if obj.type == 'MESH':
        print(f"  {obj.name} at {obj.location}")

# Position main body parts
positions = {
    "Eye_Left": (0.03, 0.08, 1.65),
    "Eye_Right": (-0.03, 0.08, 1.65),
    "Nose": (0, 0.09, 1.6),
    "Mouth": (0, 0.085, 1.55),
    "Hair": (0, -0.02, 1.75),
    "Hand_Left": (0.25, 0, 1.2),
    "Hand_Right": (-0.25, 0, 1.2),
    "Foot_Left": (0.1, 0, 0.05),
    "Foot_Right": (-0.1, 0, 0.05)
}

for obj_name, pos in positions.items():
    obj = bpy.data.objects.get(obj_name)
    if obj:
        obj.location = Vector(pos)
        print(f"Positioned {obj_name}")

# Position camera for full body view
camera = bpy.data.objects.get("Camera")
if camera:
    camera.location = (3, -3, 1.5)
    camera.rotation_euler = (1.1, 0, 0.785)
    print("Camera repositioned")

print("✅ Simple fix complete!")
'''
    
    with AugmentBlenderInterface() as blender:
        if blender.connected:
            print("🔧 Applying simple body fix...")
            result = blender.execute_blender_code(fix_code)
            
            if result.get('status') == 'success':
                output = result.get('result', {}).get('result', '')
                print("Fix output:")
                print(output)
                print("\n✅ Simple fix applied successfully!")
                print("\n📷 Now press F12 to render and see the improved body!")
            else:
                print(f"❌ Error: {result.get('message', 'Unknown error')}")
        else:
            print("❌ Could not connect to Blender")

if __name__ == "__main__":
    simple_body_fix()
