#!/usr/bin/env python3
"""
Create a natural-looking human body using better techniques
"""

from augment_blender_interface import AugmentBlenderInterface

def create_natural_human():
    """Create a natural-looking human body"""
    
    natural_human_code = '''
import bpy
import bmesh

print("🧑 Creating natural human body...")

# Delete the current robotic body
current_body = bpy.data.objects.get("Human_Body")
if current_body:
    bpy.data.objects.remove(current_body, do_unlink=True)
    print("🗑️ Removed robotic body")

# Get the rig
human_rig = bpy.data.objects.get("Human_Rig")
if not human_rig:
    human_rig = bpy.data.objects.get("RIG-Human_Metarig")

# Try to use Blender's Extra Objects addon for better human mesh
try:
    bpy.ops.preferences.addon_enable(module="add_mesh_extra_objects")
    print("✅ Extra Objects addon enabled")
except:
    print("⚠️ Extra Objects addon not available")

# Method 1: Try to add a human figure from Extra Objects
try:
    bpy.ops.mesh.primitive_human_add()
    human_body = bpy.context.active_object
    human_body.name = "Human_Body"
    print("✅ Created human using Extra Objects addon")
    
except:
    print("⚠️ Human primitive not available, creating custom organic body...")
    
    # Method 2: Create a more organic body using sculpted approach
    # Start with a metaball-based approach for organic shapes
    
    # Clear selection
    bpy.ops.object.select_all(action='DESELECT')
    
    # Create the main body using a subdivided cube that we'll shape organically
    bpy.ops.mesh.primitive_cube_add(size=2, location=(0, 0, 1))
    human_body = bpy.context.active_object
    human_body.name = "Human_Body"
    
    # Enter edit mode to sculpt the body
    bpy.context.view_layer.objects.active = human_body
    bpy.ops.object.mode_set(mode='EDIT')
    
    # Add lots of geometry for organic shaping
    bpy.ops.mesh.subdivide(number_cuts=4)
    bpy.ops.mesh.subdivide(number_cuts=2)
    
    # Get the mesh
    mesh = bmesh.new()
    mesh.from_mesh(human_body.data)

    # Scale and shape different parts of the body
    for vert in mesh.verts:
        x, y, z = vert.co
        
        # HEAD AREA (top)
        if z > 1.6:
            # Make head more spherical
            vert.co.x *= 0.4
            vert.co.y *= 0.4
            # Move head up
            vert.co.z = z * 0.3 + 1.7
            
        # NECK AREA
        elif 1.4 < z <= 1.6:
            vert.co.x *= 0.25
            vert.co.y *= 0.25
            vert.co.z = z * 0.2 + 1.6
            
        # SHOULDER/CHEST AREA
        elif 1.0 < z <= 1.4:
            # Wider shoulders, narrower depth
            vert.co.x *= 0.6
            vert.co.y *= 0.3
            vert.co.z = z * 0.4 + 1.2
            
        # WAIST AREA
        elif 0.6 < z <= 1.0:
            # Narrow waist
            vert.co.x *= 0.4
            vert.co.y *= 0.25
            vert.co.z = z * 0.3 + 0.9
            
        # HIP AREA
        elif 0.2 < z <= 0.6:
            # Wider hips
            vert.co.x *= 0.5
            vert.co.y *= 0.3
            vert.co.z = z * 0.4 + 0.6
            
        # UPPER LEG AREA
        elif -0.4 < z <= 0.2:
            # Leg separation and shaping
            if x > 0:
                vert.co.x = abs(x) * 0.3 + 0.1  # Right leg
            else:
                vert.co.x = -abs(x) * 0.3 - 0.1  # Left leg
            vert.co.y *= 0.4
            vert.co.z = z * 0.6 + 0.1
            
        # LOWER LEG AREA
        elif z <= -0.4:
            # Leg separation and shaping
            if x > 0:
                vert.co.x = abs(x) * 0.25 + 0.1  # Right leg
            else:
                vert.co.x = -abs(x) * 0.25 - 0.1  # Left leg
            vert.co.y *= 0.3
            vert.co.z = z * 0.5 - 0.3
    
    # Update the mesh
    mesh.to_mesh(human_body.data)
    mesh.free()
    human_body.data.update()
    
    # Exit edit mode
    bpy.ops.object.mode_set(mode='OBJECT')
    
    # Add arms using separate objects that we'll join
    # LEFT ARM
    bpy.ops.mesh.primitive_cylinder_add(radius=0.08, depth=0.6, location=(0.7, 0, 1.5))
    l_arm = bpy.context.active_object
    l_arm.rotation_euler = (0, 0.3, 0)  # Slight angle
    bpy.ops.object.transform_apply(rotation=True)
    
    # LEFT FOREARM
    bpy.ops.mesh.primitive_cylinder_add(radius=0.06, depth=0.5, location=(1.0, 0.1, 1.1))
    l_forearm = bpy.context.active_object
    l_forearm.rotation_euler = (0, 0.2, 0)
    bpy.ops.object.transform_apply(rotation=True)
    
    # LEFT HAND
    bpy.ops.mesh.primitive_uv_sphere_add(radius=0.08, location=(1.25, 0.15, 0.9))
    l_hand = bpy.context.active_object
    l_hand.scale = (1.2, 0.6, 1.5)
    bpy.ops.object.transform_apply(scale=True)
    
    # RIGHT ARM
    bpy.ops.mesh.primitive_cylinder_add(radius=0.08, depth=0.6, location=(-0.7, 0, 1.5))
    r_arm = bpy.context.active_object
    r_arm.rotation_euler = (0, -0.3, 0)
    bpy.ops.object.transform_apply(rotation=True)
    
    # RIGHT FOREARM
    bpy.ops.mesh.primitive_cylinder_add(radius=0.06, depth=0.5, location=(-1.0, 0.1, 1.1))
    r_forearm = bpy.context.active_object
    r_forearm.rotation_euler = (0, -0.2, 0)
    bpy.ops.object.transform_apply(rotation=True)
    
    # RIGHT HAND
    bpy.ops.mesh.primitive_uv_sphere_add(radius=0.08, location=(-1.25, 0.15, 0.9))
    r_hand = bpy.context.active_object
    r_hand.scale = (1.2, 0.6, 1.5)
    bpy.ops.object.transform_apply(scale=True)
    
    # FEET
    bpy.ops.mesh.primitive_cube_add(size=0.25, location=(0.1, 0.15, -0.7))
    l_foot = bpy.context.active_object
    l_foot.scale = (0.8, 2, 0.6)
    bpy.ops.object.transform_apply(scale=True)
    
    bpy.ops.mesh.primitive_cube_add(size=0.25, location=(-0.1, 0.15, -0.7))
    r_foot = bpy.context.active_object
    r_foot.scale = (0.8, 2, 0.6)
    bpy.ops.object.transform_apply(scale=True)
    
    # Join all parts
    parts = [l_arm, l_forearm, l_hand, r_arm, r_forearm, r_hand, l_foot, r_foot]
    
    bpy.ops.object.select_all(action='DESELECT')
    human_body.select_set(True)
    for part in parts:
        part.select_set(True)
    
    bpy.context.view_layer.objects.active = human_body
    bpy.ops.object.join()
    
    print("✅ Created organic human body")

# Apply smooth shading
bpy.ops.object.shade_smooth()

# Add subdivision surface for organic look
human_body.modifiers.new(name="Subdivision", type='SUBSURF')
human_body.modifiers["Subdivision"].levels = 2  # Higher subdivision for smoother look

# Add edge split to maintain some definition
human_body.modifiers.new(name="EdgeSplit", type='EDGE_SPLIT')
human_body.modifiers["EdgeSplit"].split_angle = 0.5

print("✅ Added subdivision and smoothing")

# Create better skin material
mat = bpy.data.materials.new(name="Natural_Skin")
mat.use_nodes = True
nodes = mat.node_tree.nodes
links = mat.node_tree.links

# Clear default nodes
for node in nodes:
    nodes.remove(node)

# Create realistic skin shader
output = nodes.new(type='ShaderNodeOutputMaterial')
output.location = (600, 0)

# Mix two skin tones for more realism
mix = nodes.new(type='ShaderNodeMixRGB')
mix.location = (200, 100)
mix.inputs[0].default_value = 0.3  # Mix factor

principled = nodes.new(type='ShaderNodeBsdfPrincipled')
principled.location = (400, 0)

# Skin color 1 (base)
principled.inputs[0].default_value = (0.85, 0.65, 0.5, 1.0)  # Warmer skin tone
principled.inputs[7].default_value = 0.3  # Roughness
principled.inputs[12].default_value = 1.4  # IOR for skin

# Add some variation with noise
noise = nodes.new(type='ShaderNodeTexNoise')
noise.location = (-200, 0)
noise.inputs[2].default_value = 15.0  # Scale
noise.inputs[3].default_value = 0.5   # Detail

# Color ramp for skin variation
ramp = nodes.new(type='ShaderNodeValToRGB')
ramp.location = (0, 0)
ramp.color_ramp.elements[0].color = (0.8, 0.6, 0.45, 1.0)  # Darker skin
ramp.color_ramp.elements[1].color = (0.9, 0.7, 0.55, 1.0)  # Lighter skin

# Connect nodes
links.new(noise.outputs['Fac'], ramp.inputs['Fac'])
links.new(ramp.outputs['Color'], mix.inputs[1])
links.new(mix.outputs['Color'], principled.inputs[0])
links.new(principled.outputs['BSDF'], output.inputs['Surface'])

# Apply material
human_body.data.materials.clear()
human_body.data.materials.append(mat)

print("✅ Applied natural skin material with variation")

# Parent to rig
if human_rig:
    bpy.ops.object.select_all(action='DESELECT')
    human_body.select_set(True)
    human_rig.select_set(True)
    bpy.context.view_layer.objects.active = human_rig
    
    try:
        bpy.ops.object.parent_set(type='ARMATURE_AUTO')
        print("✅ Natural body parented to rig")
    except Exception as e:
        print(f"⚠️ Auto weights failed: {e}")
        bpy.ops.object.parent_set(type='ARMATURE')
        print("✅ Body parented to rig (manual weights)")

print("🎉 Natural human body created!")
print("👤 Much more organic and human-like appearance!")
'''
    
    with AugmentBlenderInterface() as blender:
        if blender.connected:
            print("🧑 Creating natural human body...")
            result = blender.execute_blender_code(natural_human_code)
            
            if result.get('status') == 'success':
                output = result.get('result', {}).get('result', '')
                print("Creation output:")
                print(output)
                
                print("\n🎉 Natural human body created!")
                print("👤 The character should now look much more human-like!")
                print("🎬 Ready for natural character animation!")
            else:
                print(f"❌ Error: {result.get('message', 'Unknown error')}")
        else:
            print("❌ Could not connect to Blender")

if __name__ == "__main__":
    create_natural_human()
