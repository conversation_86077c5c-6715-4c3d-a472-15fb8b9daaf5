# 🎬 AI-Powered Blender Animation Studio

**Real-time communication between AI assistants and Blender for 3D video animation**

This project sets up **BlenderMCP** - a powerful integration that enables AI assistants to directly control Blender for creating 3D animations, models, and scenes through natural language commands.

## 🚀 What This Enables

- **🤖 AI-Assisted Animation**: Create complex 3D animations using natural language
- **🎯 Real-time Control**: Direct manipulation of Blender objects, materials, and scenes
- **🎨 Asset Integration**: Download models, textures, and HDRIs from multiple sources
- **⚡ Code Execution**: Run custom Python scripts in Blender via AI commands
- **📸 Scene Analysis**: AI can see and understand your current Blender scene
- **🎭 Procedural Creation**: Generate 3D models using AI (Hyper3D Rodin)

## 📁 Project Structure

```
blender/
├── addon.py                     # Blender MCP addon (install in Blender)
├── BLENDER_MCP_SETUP_GUIDE.md   # Complete setup instructions
├── test_blender_connection.py   # Test script to verify setup
├── example_animations.py        # Sample animation scripts
└── README.md                    # This file
```

## 🛠️ Quick Setup

### 1. Prerequisites
- ✅ **uv package manager** (already installed)
- 🔲 **Blender 3.0+** ([download here](https://www.blender.org/download/))
- 🔲 **Claude Desktop** or compatible AI client

### 2. Install Blender Addon
1. Open Blender
2. Go to **Edit > Preferences > Add-ons**
3. Click **"Install..."** and select `addon.py`
4. ✅ **Enable "Interface: Blender MCP"**

### 3. Configure AI Client
Add this to your Claude Desktop config (`claude_desktop_config.json`):

```json
{
    "mcpServers": {
        "blender": {
            "command": "uvx",
            "args": ["blender-mcp"]
        }
    }
}
```

### 4. Start Connection
1. **In Blender**: Press `N` → Find "BlenderMCP" tab → Click "Connect to MCP server"
2. **In Claude**: Look for the 🔨 hammer icon → Test with "Get my Blender scene info"

### 5. Test Setup
```bash
python test_blender_connection.py
```

## 🎯 Example Commands

Once connected, try these AI commands:

### Basic Scene Control
- *"Get information about my current Blender scene"*
- *"Create a red cube at position (2, 0, 0)"*
- *"Delete the default cube and create a monkey head"*
- *"Take a screenshot of the current viewport"*

### Animation Creation
- *"Create a bouncing ball animation"*
- *"Make the cube rotate while changing colors"*
- *"Set up a camera that orbits around the scene"*
- *"Create a particle fountain effect"*

### Material & Lighting
- *"Create a metallic gold material and apply it to the cube"*
- *"Set up studio lighting for product visualization"*
- *"Add a realistic wood texture to the plane"*

### Asset Integration
- *"Download a forest HDRI from Poly Haven and set it as the world background"*
- *"Search for rock textures and apply one to the cube"*
- *"Generate a 3D model of a garden gnome using AI"*

### Advanced Workflows
- *"Create a low poly dungeon scene with a dragon guarding treasure"*
- *"Set up a beach scene with realistic materials and HDRI lighting"*
- *"Create an isometric view of the scene and render it"*

## 🎨 Features

### Core Capabilities
- ✅ **Object Manipulation** - Create, modify, delete 3D objects
- ✅ **Material Control** - Apply and modify materials/colors
- ✅ **Scene Inspection** - Get detailed scene information
- ✅ **Code Execution** - Run arbitrary Python code in Blender
- ✅ **Animation Tools** - Create keyframe animations
- ✅ **Camera Control** - Position and animate cameras

### Asset Integrations
- 🌍 **Poly Haven** - Free textures, HDRIs, and 3D models
- 🤖 **Hyper3D Rodin** - AI-generated 3D models from text
- 🎨 **Sketchfab** - Thousands of downloadable 3D models (API key required)

### Advanced Features
- 📸 **Viewport Screenshots** - AI can see your current scene
- 🔄 **Two-way Communication** - Real-time feedback between AI and Blender
- 🎬 **Animation Scripting** - Complex procedural animations
- 🎯 **Batch Operations** - Process multiple objects at once

## 📚 Documentation

- **[Complete Setup Guide](BLENDER_MCP_SETUP_GUIDE.md)** - Detailed installation instructions
- **[Example Animations](example_animations.py)** - Ready-to-use animation scripts
- **[Test Script](test_blender_connection.py)** - Verify your setup works

## 🔧 Troubleshooting

### Connection Issues
```bash
# Test if connection works
python test_blender_connection.py
```

### Common Problems
- **"Connection refused"** → Make sure Blender is running and addon is connected
- **"No hammer icon in Claude"** → Restart Claude Desktop after config change
- **"Addon not found"** → Verify addon is installed and enabled in Blender preferences

### Getting Help
1. Check the [Setup Guide](BLENDER_MCP_SETUP_GUIDE.md) for detailed troubleshooting
2. Verify all prerequisites are installed
3. Test with simple commands first before complex animations

## 🎬 Example Workflows

### 1. Product Visualization
```
"Create a studio lighting setup with three point lights, add a metallic product on a reflective surface, and position the camera for a professional product shot"
```

### 2. Architectural Visualization
```
"Create a simple room with walls, floor, and ceiling, add realistic materials, set up natural lighting with an HDRI, and create a walkthrough camera animation"
```

### 3. Character Animation
```
"Import a character model, create a simple walk cycle animation, add a ground plane with grass texture, and set up dramatic lighting"
```

### 4. Motion Graphics
```
"Create 10 cubes in a grid, animate them with staggered rotation and scale, add colorful materials that change over time, and create a dynamic camera movement"
```

## 🌟 Next Steps

Once you have the basic setup working:

1. **Experiment** with different AI commands
2. **Explore** asset downloading from Poly Haven and Sketchfab
3. **Try** AI-generated 3D models with Hyper3D
4. **Create** complex scenes by combining multiple commands
5. **Build** custom animation workflows

## 🎯 Pro Tips

- Start with simple commands and build complexity gradually
- Use the viewport screenshot feature to let AI see your current scene
- Combine multiple asset sources for rich, detailed scenes
- Save your work frequently when experimenting with new commands
- Use the test script to verify connection before starting complex work

---

**Happy animating with AI! 🎬✨**

*Transform your 3D animation workflow with the power of AI assistance.*
