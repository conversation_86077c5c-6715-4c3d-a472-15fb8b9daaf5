#!/usr/bin/env python3
"""
Test script to verify Blender MCP connection is working
Run this script to test if the MCP server can communicate with Blender
"""

import socket
import json
import time

def test_blender_connection(host='localhost', port=9876):
    """Test connection to Blender MCP server"""
    print("🔧 Testing Blender MCP Connection...")
    print(f"📡 Connecting to {host}:{port}")
    
    try:
        # Create socket connection
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)  # 10 second timeout
        
        # Connect to Blender
        sock.connect((host, port))
        print("✅ Connected to Blender MCP server!")
        
        # Test 1: Get scene info
        print("\n🧪 Test 1: Getting scene information...")
        test_command = {
            "type": "get_scene_info",
            "params": {}
        }
        
        sock.sendall(json.dumps(test_command).encode('utf-8'))
        response = sock.recv(8192).decode('utf-8')
        result = json.loads(response)
        
        if result.get("status") == "success":
            scene_info = result.get("result", {})
            print(f"✅ Scene info received!")
            print(f"   📁 Scene name: {scene_info.get('name', 'Unknown')}")
            print(f"   🎯 Object count: {scene_info.get('object_count', 0)}")
            print(f"   🎨 Materials count: {scene_info.get('materials_count', 0)}")
        else:
            print(f"❌ Failed to get scene info: {result.get('message', 'Unknown error')}")
            return False
        
        # Test 2: Execute simple code
        print("\n🧪 Test 2: Executing simple Blender code...")
        test_command = {
            "type": "execute_code",
            "params": {
                "code": "print('Hello from Blender MCP!')\nprint(f'Blender version: {bpy.app.version_string}')"
            }
        }
        
        sock.sendall(json.dumps(test_command).encode('utf-8'))
        response = sock.recv(8192).decode('utf-8')
        result = json.loads(response)
        
        if result.get("status") == "success":
            output = result.get("result", {}).get("result", "")
            print("✅ Code executed successfully!")
            print(f"   📝 Output: {output.strip()}")
        else:
            print(f"❌ Failed to execute code: {result.get('message', 'Unknown error')}")
            return False
        
        # Test 3: Check addon status
        print("\n🧪 Test 3: Checking addon integrations...")
        
        # Check Poly Haven status
        test_command = {
            "type": "get_polyhaven_status",
            "params": {}
        }
        
        sock.sendall(json.dumps(test_command).encode('utf-8'))
        response = sock.recv(8192).decode('utf-8')
        result = json.loads(response)
        
        if result.get("status") == "success":
            polyhaven_info = result.get("result", {})
            print(f"   🌍 Poly Haven: {'✅ Enabled' if polyhaven_info.get('enabled') else '⚪ Disabled'}")
        
        # Check Hyper3D status
        test_command = {
            "type": "get_hyper3d_status",
            "params": {}
        }
        
        sock.sendall(json.dumps(test_command).encode('utf-8'))
        response = sock.recv(8192).decode('utf-8')
        result = json.loads(response)
        
        if result.get("status") == "success":
            hyper3d_info = result.get("result", {})
            print(f"   🤖 Hyper3D: {'✅ Enabled' if hyper3d_info.get('enabled') else '⚪ Disabled'}")
        
        # Check Sketchfab status
        test_command = {
            "type": "get_sketchfab_status",
            "params": {}
        }
        
        sock.sendall(json.dumps(test_command).encode('utf-8'))
        response = sock.recv(8192).decode('utf-8')
        result = json.loads(response)
        
        if result.get("status") == "success":
            sketchfab_info = result.get("result", {})
            print(f"   🎨 Sketchfab: {'✅ Enabled' if sketchfab_info.get('enabled') else '⚪ Disabled'}")
        
        sock.close()
        
        print("\n🎉 All tests passed! Blender MCP is working correctly!")
        print("\n📋 Next steps:")
        print("   1. Open Claude Desktop")
        print("   2. Look for the hammer icon 🔨 in the chat")
        print("   3. Try asking: 'Can you get information about my Blender scene?'")
        print("   4. Start creating amazing 3D animations with AI assistance!")
        
        return True
        
    except ConnectionRefusedError:
        print("❌ Connection refused!")
        print("   🔧 Make sure Blender is running")
        print("   🔧 Make sure the BlenderMCP addon is installed and enabled")
        print("   🔧 Make sure you clicked 'Connect to MCP server' in Blender")
        return False
        
    except socket.timeout:
        print("❌ Connection timeout!")
        print("   🔧 Blender might be busy or not responding")
        print("   🔧 Try restarting Blender and the addon")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False
    
    finally:
        try:
            sock.close()
        except:
            pass

if __name__ == "__main__":
    print("🚀 Blender MCP Connection Test")
    print("=" * 50)
    
    success = test_blender_connection()
    
    if success:
        print("\n🎯 Status: READY FOR AI-ASSISTED 3D ANIMATION!")
    else:
        print("\n⚠️  Status: SETUP NEEDS ATTENTION")
        print("   📖 Check the BLENDER_MCP_SETUP_GUIDE.md for troubleshooting")
    
    print("=" * 50)
