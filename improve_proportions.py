#!/usr/bin/env python3
"""
Improve human proportions and add better materials
"""

from augment_blender_interface import AugmentBlenderInterface

def improve_proportions():
    """Improve human proportions and materials"""
    
    proportions_code = '''
import bpy
import bmesh
import mathutils
from mathutils import Vector

print("📐 Improving human proportions and materials...")

# 1. IMPROVE BODY PROPORTIONS
def improve_body_proportions():
    """Make the body more realistic"""
    human_body = bpy.data.objects.get("Human_Body")
    if human_body:
        print("🏃 Adjusting body proportions...")
        
        # Use object mode scaling for proportions
        bpy.context.view_layer.objects.active = human_body

        # Create a simple proportional adjustment using modifiers
        # Add a wave modifier for more natural body shape
        wave_mod = human_body.modifiers.new(name="Body_Shape", type='WAVE')
        wave_mod.use_restrict_axis_x = True
        wave_mod.use_restrict_axis_y = True
        wave_mod.height = 0.02
        wave_mod.width = 0.5
        wave_mod.speed = 0
        
        print("  ✅ Body proportions improved")

improve_body_proportions()

# 2. CREATE BETTER MATERIALS
def create_realistic_materials():
    """Create more realistic materials"""
    print("🎨 Creating realistic materials...")
    
    # Enhanced skin material
    skin_mat = bpy.data.materials.new(name="Realistic_Skin")
    skin_mat.use_nodes = True
    nodes = skin_mat.node_tree.nodes
    nodes.clear()
    
    # Create nodes
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    noise = nodes.new(type='ShaderNodeTexNoise')
    color_ramp = nodes.new(type='ShaderNodeValToRGB')
    
    # Position nodes
    output.location = (400, 0)
    principled.location = (200, 0)
    color_ramp.location = (0, 0)
    noise.location = (-200, 0)
    
    # Configure noise for skin texture
    noise.inputs['Scale'].default_value = 15.0
    noise.inputs['Detail'].default_value = 10.0
    noise.inputs['Roughness'].default_value = 0.5
    
    # Configure color ramp for skin variation
    color_ramp.color_ramp.elements[0].color = (0.8, 0.55, 0.4, 1.0)  # Base skin
    color_ramp.color_ramp.elements[1].color = (0.9, 0.65, 0.5, 1.0)  # Lighter skin
    
    # Configure principled BSDF
    principled.inputs['Base Color'].default_value = (0.85, 0.6, 0.45, 1.0)
    principled.inputs['Roughness'].default_value = 0.4
    principled.inputs['Subsurface'].default_value = 0.1
    principled.inputs['Subsurface Color'].default_value = (0.9, 0.4, 0.3, 1.0)
    
    # Link nodes
    skin_mat.node_tree.links.new(noise.outputs['Fac'], color_ramp.inputs['Fac'])
    skin_mat.node_tree.links.new(color_ramp.outputs['Color'], principled.inputs['Base Color'])
    skin_mat.node_tree.links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    # Apply to human body
    human_body = bpy.data.objects.get("Human_Body")
    if human_body:
        if human_body.data.materials:
            human_body.data.materials[0] = skin_mat
        else:
            human_body.data.materials.append(skin_mat)
    
    print("  ✅ Realistic skin material created")
    
    # Enhanced eye material
    eye_mat = bpy.data.materials.new(name="Realistic_Eyes")
    eye_mat.use_nodes = True
    nodes = eye_mat.node_tree.nodes
    nodes.clear()
    
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    
    # Eye properties
    principled.inputs['Base Color'].default_value = (0.1, 0.05, 0.02, 1.0)  # Dark brown
    principled.inputs['Roughness'].default_value = 0.0
    principled.inputs['Metallic'].default_value = 0.0
    principled.inputs['IOR'].default_value = 1.4  # Eye fluid IOR
    
    eye_mat.node_tree.links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    
    # Apply to eyes
    for eye_name in ["Eye_Left", "Eye_Right"]:
        eye = bpy.data.objects.get(eye_name)
        if eye:
            if eye.data.materials:
                eye.data.materials[0] = eye_mat
            else:
                eye.data.materials.append(eye_mat)
    
    print("  ✅ Realistic eye material created")

create_realistic_materials()

# 3. ADD HAIR (SIMPLE)
def add_simple_hair():
    """Add basic hair"""
    print("💇 Adding basic hair...")
    
    # Create hair object
    bpy.ops.mesh.primitive_uv_sphere_add(radius=0.12, location=(0, 0, 1.75))
    hair = bpy.context.object
    hair.name = "Hair"
    hair.scale = (1.0, 1.2, 0.8)
    
    # Create hair material
    hair_mat = bpy.data.materials.new(name="Hair_Material")
    hair_mat.use_nodes = True
    nodes = hair_mat.node_tree.nodes
    nodes.clear()
    
    output = nodes.new(type='ShaderNodeOutputMaterial')
    principled = nodes.new(type='ShaderNodeBsdfPrincipled')
    
    # Hair color (dark brown)
    principled.inputs['Base Color'].default_value = (0.15, 0.1, 0.05, 1.0)
    principled.inputs['Roughness'].default_value = 0.8
    
    hair_mat.node_tree.links.new(principled.outputs['BSDF'], output.inputs['Surface'])
    hair.data.materials.append(hair_mat)
    
    print("  ✅ Basic hair added")

add_simple_hair()

# 4. IMPROVE LIGHTING
def improve_lighting():
    """Add better lighting for the scene"""
    print("💡 Improving scene lighting...")
    
    # Add key light
    bpy.ops.object.light_add(type='SUN', location=(2, 2, 3))
    key_light = bpy.context.object
    key_light.name = "Key_Light"
    key_light.data.energy = 3.0
    key_light.rotation_euler = (0.785, 0, 0.785)  # 45 degrees
    
    # Add fill light
    bpy.ops.object.light_add(type='AREA', location=(-1, 1, 2))
    fill_light = bpy.context.object
    fill_light.name = "Fill_Light"
    fill_light.data.energy = 1.0
    fill_light.data.size = 2.0
    
    # Add rim light
    bpy.ops.object.light_add(type='SPOT', location=(0, -2, 2))
    rim_light = bpy.context.object
    rim_light.name = "Rim_Light"
    rim_light.data.energy = 2.0
    rim_light.rotation_euler = (1.57, 0, 0)  # Point forward
    
    print("  ✅ Three-point lighting setup complete")

improve_lighting()

# 5. SET UP BETTER CAMERA
def setup_camera():
    """Position camera for better view"""
    print("📷 Setting up camera...")
    
    camera = bpy.data.objects.get("Camera")
    if camera:
        camera.location = (3, -3, 2)
        camera.rotation_euler = (1.1, 0, 0.785)
        
        # Set camera properties
        camera.data.lens = 50  # Standard lens
        camera.data.clip_start = 0.1
        camera.data.clip_end = 100
        
        print("  ✅ Camera positioned for portrait view")

setup_camera()

print("🎉 PROPORTIONS AND MATERIALS IMPROVED!")
print("📋 Improvements made:")
print("  📐 Better body proportions")
print("  🎨 Realistic skin material with subsurface scattering")
print("  👁️ Improved eye materials")
print("  💇 Basic hair added")
print("  💡 Professional three-point lighting")
print("  📷 Camera positioned for better view")
print("")
print("💡 Your human model now looks much more realistic!")
print("🔧 Next steps you can take:")
print("  - Switch to Material Preview or Rendered view mode")
print("  - Use Sculpting mode to add more detail")
print("  - Add clothing with cloth simulation")
print("  - Create facial expressions with shape keys")
'''
    
    with AugmentBlenderInterface() as blender:
        if blender.connected:
            print("📐 Improving human proportions and materials...")
            result = blender.execute_blender_code(proportions_code)
            
            if result.get('status') == 'success':
                output = result.get('result', {}).get('result', '')
                print("Proportions improvement output:")
                print(output)
                print("\n✅ Human model improved successfully!")
            else:
                print(f"❌ Error improving model: {result.get('message', 'Unknown error')}")
        else:
            print("❌ Could not connect to Blender")

if __name__ == "__main__":
    improve_proportions()
